import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { User, PlayRecord } from '../../types';

interface UserState {
  currentUser: User | null;
  isLoggedIn: boolean;
  theme: 'light' | 'dark' | 'auto';
}

const initialState: UserState = {
  currentUser: null,
  isLoggedIn: false,
  theme: 'light',
};

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // 设置当前用户
    setCurrentUser: (state, action: PayloadAction<User>) => {
      state.currentUser = action.payload;
      state.isLoggedIn = true;
      state.theme = action.payload.preferences.theme;
    },
    
    // 用户登出
    logout: (state) => {
      state.currentUser = null;
      state.isLoggedIn = false;
    },
    
    // 设置主题
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {
      state.theme = action.payload;
      if (state.currentUser) {
        state.currentUser.preferences.theme = action.payload;
      }
    },
    
    // 添加喜欢的歌曲
    addLikedSong: (state, action: PayloadAction<string>) => {
      if (state.currentUser && !state.currentUser.likedSongs.includes(action.payload)) {
        state.currentUser.likedSongs.push(action.payload);
      }
    },
    
    // 移除喜欢的歌曲
    removeLikedSong: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.likedSongs = state.currentUser.likedSongs.filter(
          id => id !== action.payload
        );
      }
    },
    
    // 添加播放记录
    addPlayRecord: (state, action: PayloadAction<PlayRecord>) => {
      if (state.currentUser) {
        // 限制播放记录数量，保留最近的1000条
        state.currentUser.playHistory.unshift(action.payload);
        if (state.currentUser.playHistory.length > 1000) {
          state.currentUser.playHistory = state.currentUser.playHistory.slice(0, 1000);
        }
      }
    },
    
    // 关注歌手
    followArtist: (state, action: PayloadAction<string>) => {
      if (state.currentUser && !state.currentUser.followedArtists.includes(action.payload)) {
        state.currentUser.followedArtists.push(action.payload);
      }
    },
    
    // 取消关注歌手
    unfollowArtist: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.followedArtists = state.currentUser.followedArtists.filter(
          id => id !== action.payload
        );
      }
    },
    
    // 收藏歌单
    likePlaylist: (state, action: PayloadAction<string>) => {
      if (state.currentUser && !state.currentUser.likedPlaylists.includes(action.payload)) {
        state.currentUser.likedPlaylists.push(action.payload);
      }
    },
    
    // 取消收藏歌单
    unlikePlaylist: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.likedPlaylists = state.currentUser.likedPlaylists.filter(
          id => id !== action.payload
        );
      }
    },
    
    // 收藏专辑
    likeAlbum: (state, action: PayloadAction<string>) => {
      if (state.currentUser && !state.currentUser.likedAlbums.includes(action.payload)) {
        state.currentUser.likedAlbums.push(action.payload);
      }
    },
    
    // 取消收藏专辑
    unlikeAlbum: (state, action: PayloadAction<string>) => {
      if (state.currentUser) {
        state.currentUser.likedAlbums = state.currentUser.likedAlbums.filter(
          id => id !== action.payload
        );
      }
    },
    
    // 更新用户偏好设置
    updatePreferences: (state, action: PayloadAction<Partial<User['preferences']>>) => {
      if (state.currentUser) {
        state.currentUser.preferences = {
          ...state.currentUser.preferences,
          ...action.payload,
        };
      }
    },
  },
});

export const {
  setCurrentUser,
  logout,
  setTheme,
  addLikedSong,
  removeLikedSong,
  addPlayRecord,
  followArtist,
  unfollowArtist,
  likePlaylist,
  unlikePlaylist,
  likeAlbum,
  unlikeAlbum,
  updatePreferences,
} = userSlice.actions;

export default userSlice.reducer;
