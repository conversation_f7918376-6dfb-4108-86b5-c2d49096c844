// 歌曲数据类型
export interface Song {
  id: string;
  name: string;
  artist: string;
  artistId: string;
  album: string;
  albumId: string;
  duration: number; // 秒
  url: string;
  cover: string;
  lyrics?: string;
  playCount: number;
  likeCount: number;
  releaseDate: string;
  genre: string[];
}

// 歌单数据类型
export interface Playlist {
  id: string;
  name: string;
  description: string;
  cover: string;
  creator: string;
  creatorId: string;
  songs: Song[];
  playCount: number;
  createTime: string;
  updateTime: string;
  tags: string[];
  isPublic: boolean;
}

// 歌手数据类型
export interface Artist {
  id: string;
  name: string;
  avatar: string;
  description: string;
  region: 'chinese' | 'western' | 'japanese' | 'korean' | 'other';
  type: 'male' | 'female' | 'group';
  songs: Song[];
  albums: Album[];
  followers: number;
  verified: boolean;
}

// 专辑数据类型
export interface Album {
  id: string;
  name: string;
  cover: string;
  artist: string;
  artistId: string;
  releaseDate: string;
  description: string;
  songs: Song[];
  genre: string[];
  playCount: number;
}

// 用户数据类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar: string;
  nickname: string;
  bio: string;
  likedSongs: string[]; // 歌曲ID数组
  likedPlaylists: string[]; // 歌单ID数组
  likedAlbums: string[]; // 专辑ID数组
  followedArtists: string[]; // 歌手ID数组
  createdPlaylists: string[]; // 创建的歌单ID数组
  playHistory: PlayRecord[];
  preferences: UserPreferences;
}

// 播放记录
export interface PlayRecord {
  songId: string;
  playTime: string;
  duration: number; // 播放时长（秒）
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  volume: number; // 0-1
  playMode: 'sequence' | 'random' | 'repeat';
  autoPlay: boolean;
  showLyrics: boolean;
}

// 播放器状态
export interface PlayerState {
  currentSong: Song | null;
  isPlaying: boolean;
  currentTime: number;
  duration: number;
  volume: number;
  playMode: 'sequence' | 'random' | 'repeat';
  playlist: Song[];
  currentIndex: number;
  showLyrics: boolean;
}

// 排行榜类型
export interface Ranking {
  id: string;
  name: string;
  description: string;
  cover: string;
  songs: RankingSong[];
  updateTime: string;
}

export interface RankingSong extends Song {
  rank: number;
  lastRank: number;
  trend: 'up' | 'down' | 'same' | 'new';
}

// API响应类型
export interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 搜索结果类型
export interface SearchResult {
  songs: Song[];
  artists: Artist[];
  albums: Album[];
  playlists: Playlist[];
  total: number;
}

// 路由参数类型
export interface RouteParams {
  id: string;
}

// 组件Props类型
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}
