import axios from 'axios';
import { ApiResponse, Song, Artist, Album, Playlist, Ranking, SearchResult } from '../types';

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加token等认证信息
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error);
    return Promise.reject(error);
  }
);

// 歌曲相关API
export const songApi = {
  // 获取热门歌曲
  getHotSongs: (): Promise<ApiResponse<Song[]>> => 
    api.get('/songs/hot'),
  
  // 获取新歌
  getNewSongs: (): Promise<ApiResponse<Song[]>> => 
    api.get('/songs/new'),
  
  // 根据ID获取歌曲
  getSongById: (id: string): Promise<ApiResponse<Song>> => 
    api.get(`/songs/${id}`),
  
  // 获取歌曲歌词
  getLyrics: (id: string): Promise<ApiResponse<string>> => 
    api.get(`/songs/${id}/lyrics`),
};

// 歌手相关API
export const artistApi = {
  // 获取所有歌手
  getArtists: (params?: { region?: string; type?: string; page?: number; limit?: number }): Promise<ApiResponse<Artist[]>> => 
    api.get('/artists', { params }),
  
  // 根据ID获取歌手详情
  getArtistById: (id: string): Promise<ApiResponse<Artist>> => 
    api.get(`/artists/${id}`),
  
  // 获取歌手的歌曲
  getArtistSongs: (id: string): Promise<ApiResponse<Song[]>> => 
    api.get(`/artists/${id}/songs`),
  
  // 获取歌手的专辑
  getArtistAlbums: (id: string): Promise<ApiResponse<Album[]>> => 
    api.get(`/artists/${id}/albums`),
};

// 专辑相关API
export const albumApi = {
  // 获取热门专辑
  getHotAlbums: (): Promise<ApiResponse<Album[]>> => 
    api.get('/albums/hot'),
  
  // 获取新专辑
  getNewAlbums: (): Promise<ApiResponse<Album[]>> => 
    api.get('/albums/new'),
  
  // 根据ID获取专辑详情
  getAlbumById: (id: string): Promise<ApiResponse<Album>> => 
    api.get(`/albums/${id}`),
};

// 歌单相关API
export const playlistApi = {
  // 获取精品歌单
  getFeaturedPlaylists: (): Promise<ApiResponse<Playlist[]>> => 
    api.get('/playlists/featured'),
  
  // 获取分类歌单
  getPlaylistsByCategory: (category: string): Promise<ApiResponse<Playlist[]>> => 
    api.get(`/playlists/category/${category}`),
  
  // 根据ID获取歌单详情
  getPlaylistById: (id: string): Promise<ApiResponse<Playlist>> => 
    api.get(`/playlists/${id}`),
  
  // 创建歌单
  createPlaylist: (data: Partial<Playlist>): Promise<ApiResponse<Playlist>> => 
    api.post('/playlists', data),
  
  // 更新歌单
  updatePlaylist: (id: string, data: Partial<Playlist>): Promise<ApiResponse<Playlist>> => 
    api.put(`/playlists/${id}`, data),
  
  // 删除歌单
  deletePlaylist: (id: string): Promise<ApiResponse<void>> => 
    api.delete(`/playlists/${id}`),
};

// 排行榜相关API
export const rankingApi = {
  // 获取所有排行榜
  getRankings: (): Promise<ApiResponse<Ranking[]>> => 
    api.get('/rankings'),
  
  // 根据ID获取排行榜详情
  getRankingById: (id: string): Promise<ApiResponse<Ranking>> => 
    api.get(`/rankings/${id}`),
};

// 搜索相关API
export const searchApi = {
  // 综合搜索
  search: (keyword: string, type?: string): Promise<ApiResponse<SearchResult>> => 
    api.get('/search', { params: { keyword, type } }),
  
  // 搜索建议
  getSuggestions: (keyword: string): Promise<ApiResponse<string[]>> => 
    api.get('/search/suggestions', { params: { keyword } }),
  
  // 热门搜索词
  getHotKeywords: (): Promise<ApiResponse<string[]>> => 
    api.get('/search/hot'),
};

// 用户相关API
export const userApi = {
  // 用户登录
  login: (credentials: { email: string; password: string }): Promise<ApiResponse<{ user: any; token: string }>> => 
    api.post('/auth/login', credentials),
  
  // 用户注册
  register: (userData: { username: string; email: string; password: string }): Promise<ApiResponse<{ user: any; token: string }>> => 
    api.post('/auth/register', userData),
  
  // 获取用户信息
  getUserInfo: (): Promise<ApiResponse<any>> => 
    api.get('/user/profile'),
  
  // 更新用户信息
  updateUserInfo: (data: any): Promise<ApiResponse<any>> => 
    api.put('/user/profile', data),
};

export default api;
