import { 
  mockSongs, 
  mockArtists, 
  mockAlbums, 
  mockPlaylists, 
  mockRankings, 
  mockUser 
} from '../data/mockData';
import { 
  Song, 
  Artist, 
  Album, 
  Playlist, 
  Ranking, 
  SearchResult, 
  ApiResponse 
} from '../types';

// 模拟网络延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 创建统一的API响应格式
const createResponse = <T>(data: T, message = 'success'): ApiResponse<T> => ({
  code: 200,
  message,
  data
});

// 模拟API服务
export const mockApiService = {
  // 歌曲相关API
  songs: {
    getHotSongs: async (): Promise<ApiResponse<Song[]>> => {
      await delay(500);
      return createResponse(mockSongs);
    },
    
    getNewSongs: async (): Promise<ApiResponse<Song[]>> => {
      await delay(500);
      return createResponse(mockSongs.slice(0, 2));
    },
    
    getSongById: async (id: string): Promise<ApiResponse<Song>> => {
      await delay(300);
      const song = mockSongs.find(s => s.id === id);
      if (!song) {
        throw new Error('歌曲不存在');
      }
      return createResponse(song);
    },
    
    getLyrics: async (id: string): Promise<ApiResponse<string>> => {
      await delay(300);
      const song = mockSongs.find(s => s.id === id);
      return createResponse(song?.lyrics || '暂无歌词');
    }
  },

  // 歌手相关API
  artists: {
    getArtists: async (params?: { 
      region?: string; 
      type?: string; 
      page?: number; 
      limit?: number 
    }): Promise<ApiResponse<Artist[]>> => {
      await delay(500);
      let filteredArtists = [...mockArtists];
      
      if (params?.region) {
        filteredArtists = filteredArtists.filter(artist => artist.region === params.region);
      }
      
      if (params?.type) {
        filteredArtists = filteredArtists.filter(artist => artist.type === params.type);
      }
      
      return createResponse(filteredArtists);
    },
    
    getArtistById: async (id: string): Promise<ApiResponse<Artist>> => {
      await delay(300);
      const artist = mockArtists.find(a => a.id === id);
      if (!artist) {
        throw new Error('歌手不存在');
      }
      return createResponse(artist);
    },
    
    getArtistSongs: async (id: string): Promise<ApiResponse<Song[]>> => {
      await delay(300);
      const artist = mockArtists.find(a => a.id === id);
      return createResponse(artist?.songs || []);
    },
    
    getArtistAlbums: async (id: string): Promise<ApiResponse<Album[]>> => {
      await delay(300);
      const artist = mockArtists.find(a => a.id === id);
      return createResponse(artist?.albums || []);
    }
  },

  // 专辑相关API
  albums: {
    getHotAlbums: async (): Promise<ApiResponse<Album[]>> => {
      await delay(500);
      return createResponse(mockAlbums);
    },
    
    getNewAlbums: async (): Promise<ApiResponse<Album[]>> => {
      await delay(500);
      return createResponse(mockAlbums.slice(0, 1));
    },
    
    getAlbumById: async (id: string): Promise<ApiResponse<Album>> => {
      await delay(300);
      const album = mockAlbums.find(a => a.id === id);
      if (!album) {
        throw new Error('专辑不存在');
      }
      return createResponse(album);
    }
  },

  // 歌单相关API
  playlists: {
    getFeaturedPlaylists: async (): Promise<ApiResponse<Playlist[]>> => {
      await delay(500);
      return createResponse(mockPlaylists);
    },
    
    getPlaylistsByCategory: async (category: string): Promise<ApiResponse<Playlist[]>> => {
      await delay(500);
      const filteredPlaylists = mockPlaylists.filter(playlist => 
        playlist.tags.includes(category)
      );
      return createResponse(filteredPlaylists);
    },
    
    getPlaylistById: async (id: string): Promise<ApiResponse<Playlist>> => {
      await delay(300);
      const playlist = mockPlaylists.find(p => p.id === id);
      if (!playlist) {
        throw new Error('歌单不存在');
      }
      return createResponse(playlist);
    },
    
    createPlaylist: async (data: Partial<Playlist>): Promise<ApiResponse<Playlist>> => {
      await delay(500);
      const newPlaylist: Playlist = {
        id: `playlist_${Date.now()}`,
        name: data.name || '新建歌单',
        description: data.description || '',
        cover: data.cover || 'https://picsum.photos/300/300?random=99',
        creator: data.creator || '用户',
        creatorId: data.creatorId || 'user1',
        songs: data.songs || [],
        playCount: 0,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString(),
        tags: data.tags || [],
        isPublic: data.isPublic ?? true
      };
      return createResponse(newPlaylist);
    },
    
    updatePlaylist: async (id: string, data: Partial<Playlist>): Promise<ApiResponse<Playlist>> => {
      await delay(500);
      const playlist = mockPlaylists.find(p => p.id === id);
      if (!playlist) {
        throw new Error('歌单不存在');
      }
      const updatedPlaylist = { ...playlist, ...data, updateTime: new Date().toISOString() };
      return createResponse(updatedPlaylist);
    },
    
    deletePlaylist: async (id: string): Promise<ApiResponse<void>> => {
      await delay(500);
      const index = mockPlaylists.findIndex(p => p.id === id);
      if (index === -1) {
        throw new Error('歌单不存在');
      }
      return createResponse(undefined, '删除成功');
    }
  },

  // 排行榜相关API
  rankings: {
    getRankings: async (): Promise<ApiResponse<Ranking[]>> => {
      await delay(500);
      return createResponse(mockRankings);
    },
    
    getRankingById: async (id: string): Promise<ApiResponse<Ranking>> => {
      await delay(300);
      const ranking = mockRankings.find(r => r.id === id);
      if (!ranking) {
        throw new Error('排行榜不存在');
      }
      return createResponse(ranking);
    }
  },

  // 搜索相关API
  search: {
    search: async (keyword: string, type?: string): Promise<ApiResponse<SearchResult>> => {
      await delay(800);
      const lowerKeyword = keyword.toLowerCase();
      
      const songs = mockSongs.filter(song => 
        song.name.toLowerCase().includes(lowerKeyword) ||
        song.artist.toLowerCase().includes(lowerKeyword)
      );
      
      const artists = mockArtists.filter(artist =>
        artist.name.toLowerCase().includes(lowerKeyword)
      );
      
      const albums = mockAlbums.filter(album =>
        album.name.toLowerCase().includes(lowerKeyword) ||
        album.artist.toLowerCase().includes(lowerKeyword)
      );
      
      const playlists = mockPlaylists.filter(playlist =>
        playlist.name.toLowerCase().includes(lowerKeyword) ||
        playlist.description.toLowerCase().includes(lowerKeyword)
      );
      
      const result: SearchResult = {
        songs,
        artists,
        albums,
        playlists,
        total: songs.length + artists.length + albums.length + playlists.length
      };
      
      return createResponse(result);
    },
    
    getSuggestions: async (keyword: string): Promise<ApiResponse<string[]>> => {
      await delay(200);
      const suggestions = [
        '周杰伦',
        'Shape of You',
        'BTS',
        '华语流行',
        '欧美音乐'
      ].filter(item => item.toLowerCase().includes(keyword.toLowerCase()));
      
      return createResponse(suggestions);
    },
    
    getHotKeywords: async (): Promise<ApiResponse<string[]>> => {
      await delay(300);
      const hotKeywords = [
        '周杰伦',
        'Taylor Swift',
        'BTS',
        '华语流行',
        '欧美金曲',
        '日韩音乐',
        '经典老歌',
        '网络热歌'
      ];
      return createResponse(hotKeywords);
    }
  },

  // 用户相关API
  user: {
    login: async (credentials: { email: string; password: string }) => {
      await delay(1000);
      // 简单的模拟登录验证
      if (credentials.email === '<EMAIL>' && credentials.password === '123456') {
        return createResponse({
          user: mockUser,
          token: 'mock_token_' + Date.now()
        });
      }
      throw new Error('用户名或密码错误');
    },
    
    register: async (userData: { username: string; email: string; password: string }) => {
      await delay(1000);
      const newUser = {
        ...mockUser,
        id: 'user_' + Date.now(),
        username: userData.username,
        email: userData.email,
        nickname: userData.username
      };
      return createResponse({
        user: newUser,
        token: 'mock_token_' + Date.now()
      });
    },
    
    getUserInfo: async () => {
      await delay(300);
      return createResponse(mockUser);
    },
    
    updateUserInfo: async (data: any) => {
      await delay(500);
      const updatedUser = { ...mockUser, ...data };
      return createResponse(updatedUser);
    }
  }
};
