import React from 'react';
import { Outlet } from 'react-router-dom';
import { Layout as AntLayout } from 'antd';
import Header from './Header';
import Sidebar from './Sidebar';
import Player from './Player';

const { Content } = AntLayout;

const Layout: React.FC = () => {
  return (
    <div className="music-player-container">
      <AntLayout className="h-full">
        {/* 顶部导航 */}
        <Header />
        
        <AntLayout className="flex-1">
          {/* 侧边栏 */}
          <Sidebar />
          
          {/* 主内容区域 */}
          <Content className="content-area bg-gray-50 dark:bg-dark-800">
            <div className="h-full p-6">
              <Outlet />
            </div>
          </Content>
        </AntLayout>
        
        {/* 底部播放器 */}
        <Player />
      </AntLayout>
    </div>
  );
};

export default Layout;
