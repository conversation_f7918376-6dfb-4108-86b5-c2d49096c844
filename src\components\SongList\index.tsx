import React from 'react';
import { Table, Button, Avatar, Typography, message } from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  HeartOutlined,
  HeartFilled,
  PlusOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { setCurrentSong, setPlaylist, addToPlaylist } from '../../store/slices/playerSlice';
import { addLikedSong, removeLikedSong } from '../../store/slices/userSlice';
import { formatTime, formatPlayCount } from '../../utils';
import { Song } from '../../types';

const { Text } = Typography;

interface SongListProps {
  songs: Song[];
  showIndex?: boolean;
  showAlbum?: boolean;
  showPlayCount?: boolean;
  loading?: boolean;
  onPlay?: (song: Song, songs: Song[]) => void;
}

const SongList: React.FC<SongListProps> = ({
  songs,
  showIndex = true,
  showAlbum = true,
  showPlayCount = true,
  loading = false,
  onPlay
}) => {
  const dispatch = useDispatch();
  const { currentSong, isPlaying } = useSelector((state: RootState) => state.player);
  const { currentUser, isLoggedIn } = useSelector((state: RootState) => state.user);

  // 播放歌曲
  const handlePlay = (song: Song) => {
    if (onPlay) {
      onPlay(song, songs);
    } else {
      // 默认行为：设置当前歌曲并更新播放列表
      dispatch(setCurrentSong(song));
      dispatch(setPlaylist(songs));
    }
  };

  // 添加到播放列表
  const handleAddToPlaylist = (song: Song, event: React.MouseEvent) => {
    event.stopPropagation();
    dispatch(addToPlaylist(song));
    message.success('已添加到播放队列');
  };

  // 喜欢/取消喜欢
  const handleLike = (song: Song, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (!isLoggedIn) {
      message.warning('请先登录');
      return;
    }
    
    const isLiked = currentUser?.likedSongs.includes(song.id);
    
    if (isLiked) {
      dispatch(removeLikedSong(song.id));
      message.success('已取消收藏');
    } else {
      dispatch(addLikedSong(song.id));
      message.success('已添加到我喜欢的音乐');
    }
  };

  const columns = [
    // 序号/播放按钮列
    {
      title: showIndex ? '#' : '',
      key: 'index',
      width: 60,
      render: (_: any, song: Song, index: number) => {
        const isCurrentSong = currentSong?.id === song.id;
        const isCurrentPlaying = isCurrentSong && isPlaying;

        return (
          <div className="flex items-center justify-center">
            {isCurrentSong ? (
              <Button
                type="text"
                size="small"
                icon={isCurrentPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                onClick={() => handlePlay(song)}
                className="text-primary-600"
              />
            ) : (
              <div className="group-hover:hidden text-gray-500 text-sm">
                {showIndex ? index + 1 : ''}
              </div>
            )}
            <Button
              type="text"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handlePlay(song)}
              className="hidden group-hover:flex text-gray-600 hover:text-primary-600"
            />
          </div>
        );
      }
    },
    // 歌曲信息列
    {
      title: '歌曲',
      key: 'song',
      render: (_: any, song: Song) => {
        const isCurrentSong = currentSong?.id === song.id;
        
        return (
          <div className="flex items-center space-x-3">
            <Avatar 
              src={song.cover} 
              size={48}
              className="rounded-lg flex-shrink-0"
            />
            <div className="flex-1 min-w-0">
              <div className={`font-medium truncate ${
                isCurrentSong ? 'text-primary-600' : 'text-gray-900 dark:text-white'
              }`}>
                {song.name}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                {song.artist}
              </div>
            </div>
          </div>
        );
      }
    },
    // 专辑列（可选）
    ...(showAlbum ? [{
      title: '专辑',
      key: 'album',
      width: 200,
      render: (_: any, song: Song) => (
        <Text className="text-gray-600 dark:text-gray-300 truncate">
          {song.album}
        </Text>
      )
    }] : []),
    // 播放次数列（可选）
    ...(showPlayCount ? [{
      title: '播放次数',
      key: 'playCount',
      width: 120,
      render: (_: any, song: Song) => (
        <Text className="text-gray-500 dark:text-gray-400">
          {formatPlayCount(song.playCount)}
        </Text>
      )
    }] : []),
    // 时长列
    {
      title: '时长',
      key: 'duration',
      width: 80,
      render: (_: any, song: Song) => (
        <Text className="text-gray-500 dark:text-gray-400">
          {formatTime(song.duration)}
        </Text>
      )
    },
    // 操作列
    {
      title: '',
      key: 'actions',
      width: 120,
      render: (_: any, song: Song) => {
        const isLiked = currentUser?.likedSongs.includes(song.id);
        
        return (
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              type="text"
              size="small"
              icon={isLiked ? <HeartFilled /> : <HeartOutlined />}
              onClick={(e) => handleLike(song, e)}
              className={isLiked ? 'text-red-500 hover:text-red-600' : 'text-gray-500 hover:text-red-500'}
            />
            <Button
              type="text"
              size="small"
              icon={<PlusOutlined />}
              onClick={(e) => handleAddToPlaylist(song, e)}
              className="text-gray-500 hover:text-primary-600"
            />
            <Button
              type="text"
              size="small"
              icon={<DownloadOutlined />}
              className="text-gray-500 hover:text-primary-600"
            />
          </div>
        );
      }
    }
  ];

  return (
    <Table
      dataSource={songs}
      columns={columns}
      rowKey="id"
      loading={loading}
      pagination={false}
      showHeader={false}
      rowClassName="group hover:bg-gray-50 dark:hover:bg-dark-700 cursor-pointer"
      onRow={(song) => ({
        onDoubleClick: () => handlePlay(song),
      })}
      className="song-list-table"
    />
  );
};

export default SongList;
