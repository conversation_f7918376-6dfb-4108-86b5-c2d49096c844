import React, { useEffect, useState } from 'react';
import { Card, Row, Col, Typography, Button, Spin, Carousel, message } from 'antd';
import { PlayCircleOutlined, RightOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { mockApiService } from '../../services/mockApi';
import { setCurrentSongs } from '../../store/slices/musicSlice';
import { Song, Playlist } from '../../types';
import SongList from '../../components/SongList';

const { Title, Text } = Typography;

const Home: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [hotSongs, setHotSongs] = useState<Song[]>([]);
  const [featuredPlaylists, setFeaturedPlaylists] = useState<Playlist[]>([]);

  // 加载数据
  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      try {
        // 并行加载热门歌曲和精品歌单
        const [songsResponse, playlistsResponse] = await Promise.all([
          mockApiService.songs.getHotSongs(),
          mockApiService.playlists.getFeaturedPlaylists()
        ]);

        setHotSongs(songsResponse.data);
        setFeaturedPlaylists(playlistsResponse.data);
        dispatch(setCurrentSongs(songsResponse.data));
      } catch (error) {
        console.error('Failed to load home data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dispatch]);

  // 模拟数据
  const bannerData = [
    {
      id: '1',
      title: '热门推荐',
      subtitle: '发现最新最热的音乐',
      image: 'https://picsum.photos/800/300?random=1',
      link: '/playlists'
    },
    {
      id: '2',
      title: '新歌首发',
      subtitle: '抢先听最新发布的歌曲',
      image: 'https://picsum.photos/800/300?random=2',
      link: '/ranking'
    }
  ];

  const quickEntries = [
    { title: '排行榜', icon: '🏆', link: '/ranking', color: 'bg-red-500' },
    { title: '歌手', icon: '👨‍🎤', link: '/artists', color: 'bg-blue-500' },
    { title: '歌单广场', icon: '📋', link: '/playlists', color: 'bg-green-500' },
    { title: '最近播放', icon: '🕒', link: '/profile/history', color: 'bg-purple-500' },
  ];



  const formatPlayCount = (count: number) => {
    if (count >= 100000000) return `${(count / 100000000).toFixed(1)}亿`;
    if (count >= 10000) return `${Math.floor(count / 10000)}万`;
    return count.toString();
  };

  return (
    <div className="space-y-8">
      {/* 轮播推荐区域 */}
      <section className="mb-8">
        <Carousel autoplay className="rounded-lg overflow-hidden shadow-lg">
          {bannerData.map((banner) => (
            <div key={banner.id}>
              <div
                className="h-64 bg-cover bg-center relative cursor-pointer hover-card"
                style={{ backgroundImage: `url(${banner.image})` }}
                onClick={() => navigate(banner.link)}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-black/60 to-transparent flex items-center">
                  <div className="text-left text-white p-8 max-w-md">
                    <Title level={1} className="text-white mb-4 text-4xl font-bold">
                      {banner.title}
                    </Title>
                    <Text className="text-gray-200 text-lg mb-6">
                      {banner.subtitle}
                    </Text>
                    <Button
                      type="primary"
                      size="large"
                      icon={<PlayCircleOutlined />}
                      className="bg-primary-600 border-primary-600 hover:bg-primary-700"
                    >
                      立即体验
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </Carousel>
      </section>

      {/* 快速入口 */}
      <section>
        <Title level={3} className="mb-4">快速入口</Title>
        <Row gutter={[16, 16]}>
          {quickEntries.map((entry) => (
            <Col xs={12} sm={6} key={entry.title}>
              <Card
                hoverable
                className="text-center border-none shadow-md cursor-pointer"
                onClick={() => navigate(entry.link)}
              >
                <div className={`w-16 h-16 ${entry.color} rounded-full flex items-center justify-center mx-auto mb-3`}>
                  <span className="text-2xl">{entry.icon}</span>
                </div>
                <Text strong className="text-gray-700 dark:text-gray-300">
                  {entry.title}
                </Text>
              </Card>
            </Col>
          ))}
        </Row>
      </section>

      {/* 推荐歌单 */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <Title level={3} className="mb-0">推荐歌单</Title>
          <Button 
            type="text" 
            icon={<RightOutlined />}
            onClick={() => navigate('/playlists')}
            className="text-primary-600"
          >
            查看更多
          </Button>
        </div>
        <Row gutter={[16, 16]}>
          {featuredPlaylists.map((playlist) => (
            <Col xs={12} sm={8} lg={6} key={playlist.id}>
              <Card
                hoverable
                cover={
                  <div className="relative">
                    <img 
                      src={playlist.cover} 
                      alt={playlist.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
                      <PlayCircleOutlined className="text-white text-4xl opacity-0 hover:opacity-100 transition-opacity duration-300" />
                    </div>
                    <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded">
                      {formatPlayCount(playlist.playCount)}
                    </div>
                  </div>
                }
                className="border-none shadow-md"
                onClick={() => navigate(`/playlist/${playlist.id}`)}
              >
                <Card.Meta
                  title={
                    <div className="truncate text-sm font-medium">
                      {playlist.name}
                    </div>
                  }
                  description={
                    <div className="text-xs text-gray-500 line-clamp-2">
                      {playlist.description}
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </section>

      {/* 最新音乐 */}
      <section>
        <div className="flex items-center justify-between mb-4">
          <Title level={3} className="mb-0">最新音乐</Title>
          <Button 
            type="text" 
            icon={<RightOutlined />}
            onClick={() => navigate('/ranking')}
            className="text-primary-600"
          >
            查看更多
          </Button>
        </div>
        <Card className="border-none shadow-md">
          {hotSongs.length > 0 ? (
            <SongList
              songs={hotSongs.slice(0, 10)}
              loading={loading}
              showIndex={true}
              showAlbum={true}
              showPlayCount={true}
            />
          ) : (
            <div className="text-center py-8 text-gray-500">
              <PlayCircleOutlined className="text-4xl mb-4" />
              <div>暂无最新音乐数据</div>
              <div className="text-sm mt-2">请稍后再试</div>
            </div>
          )}
        </Card>
      </section>
    </div>
  );
};

export default Home;
