import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Tabs, Spin, Avatar, Tag, Input } from 'antd';
import { UserOutlined, StarOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { mockApiService } from '../../services/mockApi';
import type { Artist } from '../../types';
import { formatPlayCount } from '../../utils';

const { Title } = Typography;
const { TabPane } = Tabs;
const { Search } = Input;

const Artists: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [artists, setArtists] = useState<Artist[]>([]);
  const [filteredArtists, setFilteredArtists] = useState<Artist[]>([]);
  const [activeRegion, setActiveRegion] = useState<string>('all');
  const [activeType, setActiveType] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');

  // 地区分类
  const regions = [
    { key: 'all', label: '全部地区' },
    { key: 'chinese', label: '华语' },
    { key: 'western', label: '欧美' },
    { key: 'japanese', label: '日语' },
    { key: 'korean', label: '韩语' },
    { key: 'other', label: '其他' }
  ];

  // 类型分类
  const types = [
    { key: 'all', label: '全部类型' },
    { key: 'male', label: '男歌手' },
    { key: 'female', label: '女歌手' },
    { key: 'group', label: '组合' }
  ];

  // 加载歌手数据
  useEffect(() => {
    const loadArtists = async () => {
      setLoading(true);
      try {
        const response = await mockApiService.artists.getArtists();
        setArtists(response.data);
        setFilteredArtists(response.data);
      } catch (error) {
        console.error('Failed to load artists:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArtists();
  }, []);

  // 过滤歌手
  useEffect(() => {
    let filtered = [...artists];

    // 按地区过滤
    if (activeRegion !== 'all') {
      filtered = filtered.filter(artist => artist.region === activeRegion);
    }

    // 按类型过滤
    if (activeType !== 'all') {
      filtered = filtered.filter(artist => artist.type === activeType);
    }

    // 按搜索关键词过滤
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(artist =>
        artist.name.toLowerCase().includes(keyword) ||
        artist.description.toLowerCase().includes(keyword)
      );
    }

    setFilteredArtists(filtered);
  }, [artists, activeRegion, activeType, searchKeyword]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  // 获取歌手类型标签颜色
  const getTypeTagColor = (type: string) => {
    switch (type) {
      case 'male': return 'blue';
      case 'female': return 'pink';
      case 'group': return 'purple';
      default: return 'default';
    }
  };

  // 获取地区标签颜色
  const getRegionTagColor = (region: string) => {
    switch (region) {
      case 'chinese': return 'red';
      case 'western': return 'blue';
      case 'japanese': return 'orange';
      case 'korean': return 'green';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="mb-0 flex items-center">
          <UserOutlined className="mr-3 text-blue-500" />
          歌手
        </Title>
        <div className="flex items-center space-x-4">
          <Search
            placeholder="搜索歌手..."
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
        </div>
      </div>

      {/* 筛选标签 */}
      <Card className="border-none shadow-lg">
        <Tabs
          activeKey={activeRegion}
          onChange={setActiveRegion}
          className="mb-4"
        >
          {regions.map(region => (
            <TabPane tab={region.label} key={region.key} />
          ))}
        </Tabs>

        <div className="flex flex-wrap gap-2">
          {types.map(type => (
            <Tag
              key={type.key}
              color={activeType === type.key ? 'blue' : 'default'}
              className="cursor-pointer px-3 py-1"
              onClick={() => setActiveType(type.key)}
            >
              {type.label}
            </Tag>
          ))}
        </div>
      </Card>

      {/* 歌手列表 */}
      <Row gutter={[16, 16]}>
        {filteredArtists.map((artist) => (
          <Col xs={12} sm={8} md={6} lg={4} key={artist.id}>
            <Card
              hoverable
              className="border-none shadow-md hover-card text-center"
              cover={
                <div className="relative p-4">
                  <Avatar
                    src={artist.avatar}
                    size={120}
                    className="mx-auto"
                    icon={<UserOutlined />}
                  />
                  {artist.verified && (
                    <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                      <StarOutlined className="text-xs" />
                    </div>
                  )}
                  <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center rounded-lg">
                    <PlayCircleOutlined className="text-white text-3xl opacity-0 hover:opacity-100 transition-opacity duration-300" />
                  </div>
                </div>
              }
              onClick={() => navigate(`/artist/${artist.id}`)}
            >
              <Card.Meta
                title={
                  <div className="space-y-2">
                    <div className="font-medium text-gray-900 dark:text-white truncate">
                      {artist.name}
                    </div>
                    <div className="flex justify-center space-x-1">
                      <Tag
                        color={getRegionTagColor(artist.region)}
                        className="text-xs"
                      >
                        {regions.find(r => r.key === artist.region)?.label}
                      </Tag>
                      <Tag
                        color={getTypeTagColor(artist.type)}
                        className="text-xs"
                      >
                        {types.find(t => t.key === artist.type)?.label}
                      </Tag>
                    </div>
                  </div>
                }
                description={
                  <div className="space-y-1">
                    <div className="text-xs text-gray-500 line-clamp-2">
                      {artist.description}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatPlayCount(artist.followers)} 粉丝
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {filteredArtists.length === 0 && (
        <Card className="border-none shadow-lg">
          <div className="text-center py-12 text-gray-500">
            <UserOutlined className="text-4xl mb-4" />
            <div>没有找到符合条件的歌手</div>
            <div className="text-sm mt-2">请尝试调整筛选条件</div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Artists;
