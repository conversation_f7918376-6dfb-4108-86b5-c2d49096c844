import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Song, PlayerState } from '../../types';

const initialState: PlayerState = {
  currentSong: null,
  isPlaying: false,
  currentTime: 0,
  duration: 0,
  volume: 0.8,
  playMode: 'sequence',
  playlist: [],
  currentIndex: -1,
  showLyrics: false,
};

const playerSlice = createSlice({
  name: 'player',
  initialState,
  reducers: {
    // 设置当前播放歌曲
    setCurrentSong: (state, action: PayloadAction<Song>) => {
      state.currentSong = action.payload;
      const index = state.playlist.findIndex(song => song.id === action.payload.id);
      state.currentIndex = index !== -1 ? index : 0;
    },
    
    // 播放/暂停
    togglePlay: (state) => {
      state.isPlaying = !state.isPlaying;
    },
    
    // 设置播放状态
    setPlaying: (state, action: PayloadAction<boolean>) => {
      state.isPlaying = action.payload;
    },
    
    // 设置当前播放时间
    setCurrentTime: (state, action: PayloadAction<number>) => {
      state.currentTime = action.payload;
    },
    
    // 设置歌曲总时长
    setDuration: (state, action: PayloadAction<number>) => {
      state.duration = action.payload;
    },
    
    // 设置音量
    setVolume: (state, action: PayloadAction<number>) => {
      state.volume = Math.max(0, Math.min(1, action.payload));
    },
    
    // 设置播放模式
    setPlayMode: (state, action: PayloadAction<'sequence' | 'random' | 'repeat'>) => {
      state.playMode = action.payload;
    },
    
    // 设置播放列表
    setPlaylist: (state, action: PayloadAction<Song[]>) => {
      state.playlist = action.payload;
      if (state.currentSong && action.payload.length > 0) {
        const index = action.payload.findIndex(song => song.id === state.currentSong!.id);
        state.currentIndex = index !== -1 ? index : 0;
      }
    },
    
    // 添加歌曲到播放列表
    addToPlaylist: (state, action: PayloadAction<Song>) => {
      const exists = state.playlist.find(song => song.id === action.payload.id);
      if (!exists) {
        state.playlist.push(action.payload);
      }
    },
    
    // 从播放列表移除歌曲
    removeFromPlaylist: (state, action: PayloadAction<string>) => {
      const index = state.playlist.findIndex(song => song.id === action.payload);
      if (index !== -1) {
        state.playlist.splice(index, 1);
        // 调整当前播放索引
        if (state.currentIndex > index) {
          state.currentIndex--;
        } else if (state.currentIndex === index) {
          // 如果删除的是当前播放的歌曲
          if (state.playlist.length === 0) {
            state.currentSong = null;
            state.currentIndex = -1;
          } else {
            state.currentIndex = Math.min(state.currentIndex, state.playlist.length - 1);
            state.currentSong = state.playlist[state.currentIndex] || null;
          }
        }
      }
    },
    
    // 下一首
    nextSong: (state) => {
      if (state.playlist.length === 0) return;
      
      let nextIndex: number;
      
      switch (state.playMode) {
        case 'random':
          nextIndex = Math.floor(Math.random() * state.playlist.length);
          break;
        case 'repeat':
          nextIndex = state.currentIndex; // 单曲循环
          break;
        default: // sequence
          nextIndex = (state.currentIndex + 1) % state.playlist.length;
      }
      
      state.currentIndex = nextIndex;
      state.currentSong = state.playlist[nextIndex];
    },
    
    // 上一首
    prevSong: (state) => {
      if (state.playlist.length === 0) return;
      
      let prevIndex: number;
      
      switch (state.playMode) {
        case 'random':
          prevIndex = Math.floor(Math.random() * state.playlist.length);
          break;
        case 'repeat':
          prevIndex = state.currentIndex; // 单曲循环
          break;
        default: // sequence
          prevIndex = state.currentIndex - 1 < 0 
            ? state.playlist.length - 1 
            : state.currentIndex - 1;
      }
      
      state.currentIndex = prevIndex;
      state.currentSong = state.playlist[prevIndex];
    },
    
    // 切换歌词显示
    toggleLyrics: (state) => {
      state.showLyrics = !state.showLyrics;
    },
    
    // 清空播放列表
    clearPlaylist: (state) => {
      state.playlist = [];
      state.currentSong = null;
      state.currentIndex = -1;
      state.isPlaying = false;
    },
  },
});

export const {
  setCurrentSong,
  togglePlay,
  setPlaying,
  setCurrentTime,
  setDuration,
  setVolume,
  setPlayMode,
  setPlaylist,
  addToPlaylist,
  removeFromPlaylist,
  nextSong,
  prevSong,
  toggleLyrics,
  clearPlaylist,
} = playerSlice.actions;

export default playerSlice.reducer;
