import React, { useState } from 'react';
import { Drawer, <PERSON>, Button, Avatar, Typography, Empty, Tooltip } from 'antd';
import { 
  DeleteOutlined, 
  PlayCircleOutlined,
  PauseCircleOutlined,
  DragOutlined,
  ClearOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { 
  setCurrentSong, 
  removeFromPlaylist, 
  clearPlaylist 
} from '../../store/slices/playerSlice';
import { formatTime } from '../../utils';
import { Song } from '../../types';

const { Text } = Typography;

interface PlayQueueProps {
  visible: boolean;
  onClose: () => void;
}

const PlayQueue: React.FC<PlayQueueProps> = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const { playlist, currentSong, isPlaying, currentIndex } = useSelector(
    (state: RootState) => state.player
  );

  const handlePlaySong = (song: Song, index: number) => {
    dispatch(setCurrentSong(song));
  };

  const handleRemoveSong = (songId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    dispatch(removeFromPlaylist(songId));
  };

  const handleClearPlaylist = () => {
    dispatch(clearPlaylist());
  };

  const renderSongItem = (song: Song, index: number) => {
    const isCurrentSong = currentSong?.id === song.id;
    const isCurrentPlaying = isCurrentSong && isPlaying;

    return (
      <List.Item
        key={song.id}
        className={`cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-700 px-4 py-3 ${
          isCurrentSong ? 'bg-primary-50 dark:bg-primary-900/20' : ''
        }`}
        onClick={() => handlePlaySong(song, index)}
        actions={[
          <Tooltip title="移除">
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={(e) => handleRemoveSong(song.id, e)}
              className="text-gray-400 hover:text-red-500"
            />
          </Tooltip>
        ]}
      >
        <List.Item.Meta
          avatar={
            <div className="relative">
              <Avatar 
                src={song.cover} 
                size={48}
                className="rounded-lg"
              />
              {isCurrentSong && (
                <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 rounded-lg">
                  {isCurrentPlaying ? (
                    <PauseCircleOutlined className="text-white text-lg" />
                  ) : (
                    <PlayCircleOutlined className="text-white text-lg" />
                  )}
                </div>
              )}
            </div>
          }
          title={
            <div className="flex items-center space-x-2">
              <Text 
                strong={isCurrentSong}
                className={`truncate ${isCurrentSong ? 'text-primary-600' : 'text-gray-900 dark:text-white'}`}
              >
                {song.name}
              </Text>
              {isCurrentSong && (
                <div className="flex items-center space-x-1">
                  <div className="w-1 h-1 bg-primary-600 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-primary-600 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-1 h-1 bg-primary-600 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
              )}
            </div>
          }
          description={
            <div className="flex items-center justify-between">
              <Text className="text-gray-500 dark:text-gray-400 truncate">
                {song.artist}
              </Text>
              <Text className="text-gray-400 text-xs">
                {formatTime(song.duration)}
              </Text>
            </div>
          }
        />
      </List.Item>
    );
  };

  return (
    <Drawer
      title={
        <div className="flex items-center justify-between">
          <span>播放队列 ({playlist.length})</span>
          {playlist.length > 0 && (
            <Button
              type="text"
              size="small"
              icon={<ClearOutlined />}
              onClick={handleClearPlaylist}
              className="text-gray-500 hover:text-red-500"
            >
              清空
            </Button>
          )}
        </div>
      }
      placement="right"
      width={400}
      open={visible}
      onClose={onClose}
      className="play-queue-drawer"
    >
      {playlist.length === 0 ? (
        <Empty
          description="播放队列为空"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      ) : (
        <div className="h-full overflow-hidden">
          <List
            dataSource={playlist}
            renderItem={renderSongItem}
            className="h-full overflow-y-auto"
            split={false}
          />
        </div>
      )}
    </Drawer>
  );
};

export default PlayQueue;
