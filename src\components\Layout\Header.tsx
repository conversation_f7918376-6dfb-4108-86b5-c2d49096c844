import React, { useState } from 'react';
import { Layout, Input, Button, Avatar, Dropdown, Switch } from 'antd';
import { 
  SearchOutlined, 
  UserOutlined, 
  SettingOutlined, 
  LogoutOutlined,
  SunOutlined,
  MoonOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { setTheme } from '../../store/slices/userSlice';

const { Header: AntHeader } = Layout;
const { Search } = Input;

const Header: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { theme, isLoggedIn, currentUser } = useSelector((state: RootState) => state.user);
  const [searchValue, setSearchValue] = useState('');

  const handleSearch = (value: string) => {
    if (value.trim()) {
      navigate(`/search?q=${encodeURIComponent(value.trim())}`);
    }
  };

  const handleThemeChange = (checked: boolean) => {
    dispatch(setTheme(checked ? 'dark' : 'light'));
    // 更新HTML类名以应用暗黑模式
    if (checked) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  return (
    <AntHeader className="bg-white dark:bg-dark-900 border-b border-gray-200 dark:border-dark-700 px-6 flex items-center justify-between h-16">
      {/* Logo */}
      <div 
        className="flex items-center cursor-pointer"
        onClick={() => navigate('/')}
      >
        <div className="text-2xl font-bold text-primary-600 dark:text-primary-400">
          🎵 MusicPlayer
        </div>
      </div>

      {/* 搜索框 */}
      <div className="flex-1 max-w-md mx-8">
        <Search
          placeholder="搜索歌曲、歌手、专辑..."
          allowClear
          enterButton={<SearchOutlined />}
          size="large"
          value={searchValue}
          onChange={(e) => setSearchValue(e.target.value)}
          onSearch={handleSearch}
          className="w-full"
        />
      </div>

      {/* 右侧操作区 */}
      <div className="flex items-center space-x-4">
        {/* 主题切换 */}
        <div className="flex items-center space-x-2">
          <SunOutlined className="text-gray-500" />
          <Switch
            checked={theme === 'dark'}
            onChange={handleThemeChange}
            size="small"
          />
          <MoonOutlined className="text-gray-500" />
        </div>

        {/* 用户信息 */}
        {isLoggedIn && currentUser ? (
          <Dropdown
            menu={{ items: userMenuItems }}
            placement="bottomRight"
            trigger={['click']}
          >
            <div className="flex items-center space-x-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-700 px-3 py-2 rounded-lg transition-colors">
              <Avatar 
                src={currentUser.avatar} 
                icon={<UserOutlined />}
                size="small"
              />
              <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {currentUser.nickname || currentUser.username}
              </span>
            </div>
          </Dropdown>
        ) : (
          <div className="flex items-center space-x-2">
            <Button type="text" onClick={() => navigate('/login')}>
              登录
            </Button>
            <Button type="primary" onClick={() => navigate('/register')}>
              注册
            </Button>
          </div>
        )}
      </div>
    </AntHeader>
  );
};

export default Header;
