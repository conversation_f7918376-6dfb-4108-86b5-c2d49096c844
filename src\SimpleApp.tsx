import React from 'react';
import { BrowserRouter, Routes, Route, Link } from 'react-router-dom';
import { Provider } from 'react-redux';
import { ConfigProvider, Layout, Menu, Card, Typography, Button } from 'antd';
import { HomeOutlined, TrophyOutlined, UserOutlined, AppstoreOutlined } from '@ant-design/icons';
import zhCN from 'antd/locale/zh_CN';
import { store } from './store';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;

// 简单的首页组件
const HomePage = () => (
  <Card>
    <Title level={2}>🎵 音乐播放器首页</Title>
    <Text>欢迎使用音乐播放器！</Text>
    <br /><br />
    <Button type="primary">开始体验</Button>
  </Card>
);

// 简单的排行榜组件
const RankingPage = () => (
  <Card>
    <Title level={2}>🏆 排行榜</Title>
    <Text>这里是音乐排行榜页面</Text>
  </Card>
);

// 简单的歌手页面
const ArtistsPage = () => (
  <Card>
    <Title level={2}>👨‍🎤 歌手</Title>
    <Text>这里是歌手页面</Text>
  </Card>
);

// 简单的歌单页面
const PlaylistsPage = () => (
  <Card>
    <Title level={2}>📋 歌单广场</Title>
    <Text>这里是歌单广场页面</Text>
  </Card>
);

const SimpleApp: React.FC = () => {
  const menuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: <Link to="/">首页</Link>,
    },
    {
      key: '/ranking',
      icon: <TrophyOutlined />,
      label: <Link to="/ranking">排行榜</Link>,
    },
    {
      key: '/artists',
      icon: <UserOutlined />,
      label: <Link to="/artists">歌手</Link>,
    },
    {
      key: '/playlists',
      icon: <AppstoreOutlined />,
      label: <Link to="/playlists">歌单广场</Link>,
    },
  ];

  return (
    <Provider store={store}>
      <ConfigProvider locale={zhCN}>
        <BrowserRouter>
          <Layout style={{ minHeight: '100vh' }}>
            <Header style={{ background: '#fff', padding: '0 24px', borderBottom: '1px solid #f0f0f0' }}>
              <Title level={3} style={{ margin: 0, lineHeight: '64px' }}>
                🎵 音乐播放器
              </Title>
            </Header>
            
            <Layout>
              <Sider width={240} style={{ background: '#fff' }}>
                <Menu
                  mode="inline"
                  defaultSelectedKeys={['/']}
                  items={menuItems}
                  style={{ height: '100%', borderRight: 0 }}
                />
              </Sider>
              
              <Content style={{ padding: '24px', background: '#f5f5f5' }}>
                <Routes>
                  <Route path="/" element={<HomePage />} />
                  <Route path="/ranking" element={<RankingPage />} />
                  <Route path="/artists" element={<ArtistsPage />} />
                  <Route path="/playlists" element={<PlaylistsPage />} />
                </Routes>
              </Content>
            </Layout>
          </Layout>
        </BrowserRouter>
      </ConfigProvider>
    </Provider>
  );
};

export default SimpleApp;
