import { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../store';
import { 
  setPlaying, 
  setCurrentTime, 
  setDuration, 
  nextSong,
  setCurrentSong 
} from '../store/slices/playerSlice';
import { addPlayRecord } from '../store/slices/userSlice';
import { audioService } from '../services/audioService';
import { Song } from '../types';

export const useAudioPlayer = () => {
  const dispatch = useDispatch();
  const { 
    currentSong, 
    isPlaying, 
    volume, 
    playMode 
  } = useSelector((state: RootState) => state.player);

  // 初始化音频服务回调
  useEffect(() => {
    // 时间更新回调
    audioService.setOnTimeUpdate((currentTime: number) => {
      dispatch(setCurrentTime(currentTime));
    });

    // 播放结束回调
    audioService.setOnEnd(() => {
      dispatch(setPlaying(false));
      
      // 根据播放模式决定下一步操作
      if (playMode === 'repeat') {
        // 单曲循环，重新播放当前歌曲
        if (currentSong) {
          playAudio(currentSong);
        }
      } else {
        // 自动播放下一首
        dispatch(nextSong());
      }
    });

    // 加载完成回调
    audioService.setOnLoad((duration: number) => {
      dispatch(setDuration(duration));
    });

    // 错误处理回调
    audioService.setOnError((error: any) => {
      console.error('Audio playback error:', error);
      dispatch(setPlaying(false));
      // 可以在这里添加错误提示
    });

    return () => {
      audioService.destroy();
    };
  }, [dispatch, playMode, currentSong]);

  // 监听音量变化
  useEffect(() => {
    audioService.setVolume(volume);
  }, [volume]);

  // 监听当前歌曲变化
  useEffect(() => {
    if (currentSong && currentSong !== audioService.getCurrentSong()) {
      playAudio(currentSong);
    }
  }, [currentSong]);

  // 监听播放状态变化
  useEffect(() => {
    if (isPlaying && !audioService.isPlaying()) {
      audioService.play();
    } else if (!isPlaying && audioService.isPlaying()) {
      audioService.pause();
    }
  }, [isPlaying]);

  // 播放音频
  const playAudio = async (song: Song) => {
    try {
      dispatch(setPlaying(true));
      await audioService.loadAndPlay(song);
      
      // 记录播放历史
      dispatch(addPlayRecord({
        songId: song.id,
        playTime: new Date().toISOString(),
        duration: song.duration
      }));
    } catch (error) {
      console.error('Failed to play audio:', error);
      dispatch(setPlaying(false));
    }
  };

  // 播放指定歌曲
  const playSong = (song: Song) => {
    dispatch(setCurrentSong(song));
  };

  // 切换播放/暂停
  const togglePlayPause = () => {
    if (currentSong) {
      if (isPlaying) {
        audioService.pause();
        dispatch(setPlaying(false));
      } else {
        audioService.play();
        dispatch(setPlaying(true));
      }
    }
  };

  // 跳转到指定时间
  const seekTo = (time: number) => {
    audioService.seek(time);
    dispatch(setCurrentTime(time));
  };

  // 设置音量
  const setVolumeLevel = (volume: number) => {
    audioService.setVolume(volume);
  };

  return {
    // 状态
    currentSong,
    isPlaying,
    volume,
    playMode,
    
    // 方法
    playSong,
    togglePlayPause,
    seekTo,
    setVolumeLevel,
    
    // 音频服务实例（用于获取实时信息）
    audioService
  };
};
