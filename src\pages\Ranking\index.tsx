import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Spin, Avatar, Button } from 'antd';
import { PlayCircleOutlined, TrophyOutlined, RiseOutlined, FallOutlined } from '@ant-design/icons';
import { useDispatch } from 'react-redux';
import { mockApiService } from '../../services/mockApi';
import { setCurrentSong, setPlaylist } from '../../store/slices/playerSlice';
import type { Ranking as RankingType, RankingSong } from '../../types';
import { formatPlayCount } from '../../utils';

const { Title, Text } = Typography;

const Ranking: React.FC = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [rankings, setRankings] = useState<RankingType[]>([]);
  const [activeRanking, setActiveRanking] = useState<RankingType | null>(null);

  // 加载排行榜数据
  useEffect(() => {
    const loadRankings = async () => {
      setLoading(true);
      try {
        const response = await mockApiService.rankings.getRankings();
        setRankings(response.data);
        if (response.data.length > 0) {
          setActiveRanking(response.data[0]);
        }
      } catch (error) {
        console.error('Failed to load rankings:', error);
      } finally {
        setLoading(false);
      }
    };

    loadRankings();
  }, []);

  // 处理榜单切换
  const handleRankingChange = (rankingId: string) => {
    const ranking = rankings.find(r => r.id === rankingId);
    if (ranking) {
      setActiveRanking(ranking);
    }
  };

  // 播放整个榜单
  const handlePlayAll = () => {
    if (activeRanking && activeRanking.songs.length > 0) {
      dispatch(setCurrentSong(activeRanking.songs[0]));
      dispatch(setPlaylist(activeRanking.songs));
    }
  };

  // 获取排名变化图标
  const getTrendIcon = (song: RankingSong) => {
    switch (song.trend) {
      case 'up':
        return <RiseOutlined className="text-red-500" />;
      case 'down':
        return <FallOutlined className="text-green-500" />;
      case 'new':
        return <span className="text-orange-500 text-xs font-bold">NEW</span>;
      default:
        return <span className="text-gray-400">-</span>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="mb-0 flex items-center">
          <TrophyOutlined className="mr-3 text-yellow-500" />
          音乐排行榜
        </Title>
        {activeRanking && (
          <Button
            type="primary"
            icon={<PlayCircleOutlined />}
            size="large"
            onClick={handlePlayAll}
          >
            播放全部
          </Button>
        )}
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧：排行榜列表 */}
        <Col xs={24} lg={8}>
          <Card className="border-none shadow-lg">
            <Title level={4} className="mb-4">排行榜分类</Title>
            <div className="space-y-3">
              {rankings.map((ranking) => (
                <Card
                  key={ranking.id}
                  hoverable
                  className={`cursor-pointer transition-all duration-300 ${
                    activeRanking?.id === ranking.id
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-gray-200'
                  }`}
                  onClick={() => handleRankingChange(ranking.id)}
                >
                  <div className="flex items-center space-x-4">
                    <Avatar
                      src={ranking.cover}
                      size={64}
                      className="rounded-lg"
                    />
                    <div className="flex-1 min-w-0">
                      <Title level={5} className="mb-1 truncate">
                        {ranking.name}
                      </Title>
                      <Text className="text-gray-500 text-sm block truncate">
                        {ranking.description}
                      </Text>
                      <Text className="text-gray-400 text-xs">
                        更新时间：{new Date(ranking.updateTime).toLocaleDateString()}
                      </Text>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧：排行榜详情 */}
        <Col xs={24} lg={16}>
          {activeRanking ? (
            <Card className="border-none shadow-lg">
              <div className="mb-6">
                <div className="flex items-center space-x-4 mb-4">
                  <Avatar
                    src={activeRanking.cover}
                    size={80}
                    className="rounded-lg"
                  />
                  <div>
                    <Title level={3} className="mb-2">
                      {activeRanking.name}
                    </Title>
                    <Text className="text-gray-600 dark:text-gray-300 block mb-1">
                      {activeRanking.description}
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      共 {activeRanking.songs.length} 首歌曲 •
                      更新时间：{new Date(activeRanking.updateTime).toLocaleDateString()}
                    </Text>
                  </div>
                </div>
              </div>

              {/* 排行榜歌曲列表 */}
              <div className="ranking-songs">
                {activeRanking.songs.map((song, index) => (
                  <div
                    key={song.id}
                    className="flex items-center space-x-4 p-4 hover:bg-gray-50 dark:hover:bg-dark-700 rounded-lg cursor-pointer group transition-colors"
                    onClick={() => {
                      dispatch(setCurrentSong(song));
                      dispatch(setPlaylist(activeRanking.songs));
                    }}
                  >
                    {/* 排名 */}
                    <div className="w-12 text-center">
                      <div className={`text-2xl font-bold ${
                        index < 3 ? 'text-yellow-500' : 'text-gray-500'
                      }`}>
                        {song.rank}
                      </div>
                    </div>

                    {/* 排名变化 */}
                    <div className="w-8 flex justify-center">
                      {getTrendIcon(song)}
                    </div>

                    {/* 歌曲信息 */}
                    <Avatar
                      src={song.cover}
                      size={48}
                      className="rounded-lg flex-shrink-0"
                    />

                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-900 dark:text-white truncate">
                        {song.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 truncate">
                        {song.artist}
                      </div>
                    </div>

                    {/* 播放次数 */}
                    <div className="text-right">
                      <div className="text-sm text-gray-500">
                        {formatPlayCount(song.playCount)}
                      </div>
                    </div>

                    {/* 播放按钮 */}
                    <Button
                      type="text"
                      icon={<PlayCircleOutlined />}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    />
                  </div>
                ))}
              </div>
            </Card>
          ) : (
            <Card className="border-none shadow-lg">
              <div className="text-center py-12 text-gray-500">
                <TrophyOutlined className="text-4xl mb-4" />
                <div>请选择一个排行榜查看详情</div>
              </div>
            </Card>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default Ranking;
