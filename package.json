{"name": "music-web-player", "private": true, "version": "1.0.0", "type": "module", "description": "现代化的在线音乐播放器Web应用", "keywords": ["music", "player", "react", "typescript", "vite"], "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@types/howler": "^2.2.12", "antd": "^5.26.5", "axios": "^1.10.0", "howler": "^2.2.4", "howler.js": "^2.1.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-redux": "^9.2.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}