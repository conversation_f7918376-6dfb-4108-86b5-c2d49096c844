import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Spin, Avatar, Button, Tag, Statistic, message } from 'antd';
import {
  PlayCircleOutlined,
  HeartOutlined,
  ShareAltOutlined,
  DownloadOutlined,
  UserOutlined,
  AppstoreOutlined,
  PlusOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '../../store';
import { mockApiService } from '../../services/mockApi';
import { setCurrentSong, setPlaylist as setPlayerPlaylist } from '../../store/slices/playerSlice';
import { likePlaylist, unlikePlaylist } from '../../store/slices/userSlice';
import type { Playlist } from '../../types';
import { formatPlayCount } from '../../utils';
import SongList from '../../components/SongList';

const { Title, Paragraph } = Typography;

const PlaylistDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentUser, isLoggedIn } = useSelector((state: RootState) => state.user);

  const [loading, setLoading] = useState(false);
  const [playlist, setPlaylist] = useState<Playlist | null>(null);

  // 检查是否已收藏
  const isLiked = currentUser?.likedPlaylists.includes(id || '') || false;

  // 加载歌单详情
  useEffect(() => {
    if (!id) return;

    const loadPlaylistDetail = async () => {
      setLoading(true);
      try {
        const response = await mockApiService.playlists.getPlaylistById(id);
        setPlaylist(response.data);
      } catch (error) {
        console.error('Failed to load playlist detail:', error);
        message.error('加载歌单详情失败');
      } finally {
        setLoading(false);
      }
    };

    loadPlaylistDetail();
  }, [id]);

  // 处理收藏/取消收藏
  const handleLike = () => {
    if (!isLoggedIn) {
      message.warning('请先登录');
      return;
    }

    if (!id) return;

    if (isLiked) {
      dispatch(unlikePlaylist(id));
      message.success('已取消收藏');
    } else {
      dispatch(likePlaylist(id));
      message.success('已收藏到我的歌单');
    }
  };

  // 播放全部歌曲
  const handlePlayAll = () => {
    if (playlist && playlist.songs.length > 0) {
      dispatch(setCurrentSong(playlist.songs[0]));
      dispatch(setPlayerPlaylist(playlist.songs));
    }
  };

  // 添加到播放队列
  const handleAddToQueue = () => {
    if (playlist && playlist.songs.length > 0) {
      playlist.songs.forEach(song => {
        // 这里可以添加到播放队列的逻辑
      });
      message.success('已添加到播放队列');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!playlist) {
    return (
      <Card className="border-none shadow-lg">
        <div className="text-center py-12 text-gray-500">
          <AppstoreOutlined className="text-4xl mb-4" />
          <div>歌单不存在</div>
          <Button
            type="primary"
            className="mt-4"
            onClick={() => navigate('/playlists')}
          >
            返回歌单广场
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 歌单信息头部 */}
      <Card className="border-none shadow-lg">
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={8} lg={6}>
            <div className="text-center">
              <div className="relative inline-block">
                <img
                  src={playlist.cover}
                  alt={playlist.name}
                  className="w-48 h-48 object-cover rounded-2xl shadow-lg"
                />
                <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center rounded-2xl">
                  <PlayCircleOutlined
                    className="text-white text-5xl opacity-0 hover:opacity-100 transition-opacity duration-300 cursor-pointer"
                    onClick={handlePlayAll}
                  />
                </div>
              </div>
            </div>
          </Col>

          <Col xs={24} md={16} lg={18}>
            <div className="space-y-4">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <Tag color="blue">歌单</Tag>
                  {playlist.playCount > 1000000 && (
                    <Tag color="gold">精品</Tag>
                  )}
                </div>
                <Title level={1} className="mb-3">
                  {playlist.name}
                </Title>
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <Avatar
                      size={32}
                      icon={<UserOutlined />}
                    />
                    <span className="text-gray-600 dark:text-gray-300">
                      {playlist.creator}
                    </span>
                  </div>
                  <span className="text-gray-500 text-sm">
                    {new Date(playlist.createTime).toLocaleDateString()} 创建
                  </span>
                </div>
                <Paragraph className="text-gray-600 dark:text-gray-300 text-base">
                  {playlist.description}
                </Paragraph>
              </div>

              {/* 统计信息 */}
              <Row gutter={[16, 16]}>
                <Col span={6}>
                  <Statistic
                    title="播放量"
                    value={playlist.playCount}
                    formatter={(value) => formatPlayCount(Number(value))}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="歌曲数"
                    value={playlist.songs.length}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="收藏数"
                    value={Math.floor(playlist.playCount / 100)}
                    formatter={(value) => formatPlayCount(Number(value))}
                  />
                </Col>
                <Col span={6}>
                  <Statistic
                    title="分享数"
                    value={Math.floor(playlist.playCount / 1000)}
                    formatter={(value) => formatPlayCount(Number(value))}
                  />
                </Col>
              </Row>

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3">
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handlePlayAll}
                  disabled={playlist.songs.length === 0}
                >
                  播放全部
                </Button>
                <Button
                  size="large"
                  icon={<PlusOutlined />}
                  onClick={handleAddToQueue}
                  disabled={playlist.songs.length === 0}
                >
                  添加到队列
                </Button>
                <Button
                  size="large"
                  icon={<HeartOutlined />}
                  onClick={handleLike}
                  className={isLiked ? 'text-red-500 border-red-500' : ''}
                >
                  {isLiked ? '已收藏' : '收藏'}
                </Button>
                <Button
                  size="large"
                  icon={<ShareAltOutlined />}
                >
                  分享
                </Button>
                <Button
                  size="large"
                  icon={<DownloadOutlined />}
                >
                  下载
                </Button>
                {currentUser?.id === playlist.creatorId && (
                  <Button
                    size="large"
                    icon={<EditOutlined />}
                    onClick={() => navigate(`/playlist/${id}/edit`)}
                  >
                    编辑
                  </Button>
                )}
              </div>

              {/* 标签 */}
              {playlist.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  <span className="text-gray-500 text-sm">标签：</span>
                  {playlist.tags.map(tag => (
                    <Tag
                      key={tag}
                      color="blue"
                      className="cursor-pointer"
                      onClick={() => navigate(`/playlists?tag=${tag}`)}
                    >
                      {tag}
                    </Tag>
                  ))}
                </div>
              )}
            </div>
          </Col>
        </Row>
      </Card>

      {/* 歌曲列表 */}
      <Card className="border-none shadow-lg">
        <div className="flex items-center justify-between mb-4">
          <Title level={4} className="mb-0">
            歌曲列表 ({playlist.songs.length})
          </Title>
          <div className="text-sm text-gray-500">
            总时长：{Math.floor(playlist.songs.reduce((total, song) => total + song.duration, 0) / 60)} 分钟
          </div>
        </div>

        {playlist.songs.length > 0 ? (
          <SongList
            songs={playlist.songs}
            showIndex={true}
            showAlbum={true}
            showPlayCount={false}
            onPlay={(song, songs) => {
              dispatch(setCurrentSong(song));
              dispatch(setPlayerPlaylist(songs));
            }}
          />
        ) : (
          <div className="text-center py-12 text-gray-500">
            <PlayCircleOutlined className="text-4xl mb-4" />
            <div>这个歌单还没有歌曲</div>
            <div className="text-sm mt-2">快去添加一些好听的歌曲吧</div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default PlaylistDetail;
