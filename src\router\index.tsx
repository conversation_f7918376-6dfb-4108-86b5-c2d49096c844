import { createBrowserRouter } from 'react-router-dom';
import Layout from '../components/Layout';
import Home from '../pages/Home';
import Ranking from '../pages/Ranking';
import Artists from '../pages/Artists';
import ArtistDetail from '../pages/ArtistDetail';
import Playlists from '../pages/Playlists';
import PlaylistDetail from '../pages/PlaylistDetail';
import AlbumDetail from '../pages/AlbumDetail';
import Profile from '../pages/Profile';
import Search from '../pages/Search';
import NotFound from '../pages/NotFound';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
      },
      {
        path: 'ranking',
        element: <Ranking />,
      },
      {
        path: 'artists',
        element: <Artists />,
      },
      {
        path: 'artist/:id',
        element: <ArtistDetail />,
      },
      {
        path: 'playlists',
        element: <Playlists />,
      },
      {
        path: 'playlist/:id',
        element: <PlaylistDetail />,
      },
      {
        path: 'album/:id',
        element: <AlbumDetail />,
      },
      {
        path: 'profile',
        element: <Profile />,
      },
      {
        path: 'search',
        element: <Search />,
      },
      {
        path: '*',
        element: <NotFound />,
      },
    ],
  },
]);
