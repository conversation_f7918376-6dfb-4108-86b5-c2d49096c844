import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Spin, Avatar, Button, Tag, Tabs, Statistic } from 'antd';
import {
  UserOutlined,
  PlayCircleOutlined,
  HeartOutlined,
  ShareAltOutlined,
  StarOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import type { RootState } from '../../store';
import { mockApiService } from '../../services/mockApi';
import { setCurrentSong, setPlaylist } from '../../store/slices/playerSlice';
import { followArtist, unfollowArtist } from '../../store/slices/userSlice';
import type { Artist, Song, Album } from '../../types';
import { formatPlayCount } from '../../utils';
import SongList from '../../components/SongList';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const ArtistDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { currentUser, isLoggedIn } = useSelector((state: RootState) => state.user);

  const [loading, setLoading] = useState(false);
  const [artist, setArtist] = useState<Artist | null>(null);
  const [artistSongs, setArtistSongs] = useState<Song[]>([]);
  const [artistAlbums, setArtistAlbums] = useState<Album[]>([]);

  // 检查是否已关注
  const isFollowed = currentUser?.followedArtists.includes(id || '') || false;

  // 加载歌手详情
  useEffect(() => {
    if (!id) return;

    const loadArtistDetail = async () => {
      setLoading(true);
      try {
        const [artistResponse, songsResponse, albumsResponse] = await Promise.all([
          mockApiService.artists.getArtistById(id),
          mockApiService.artists.getArtistSongs(id),
          mockApiService.artists.getArtistAlbums(id)
        ]);

        setArtist(artistResponse.data);
        setArtistSongs(songsResponse.data);
        setArtistAlbums(albumsResponse.data);
      } catch (error) {
        console.error('Failed to load artist detail:', error);
      } finally {
        setLoading(false);
      }
    };

    loadArtistDetail();
  }, [id]);

  // 处理关注/取消关注
  const handleFollow = () => {
    if (!isLoggedIn) {
      // 可以跳转到登录页面
      return;
    }

    if (!id) return;

    if (isFollowed) {
      dispatch(unfollowArtist(id));
    } else {
      dispatch(followArtist(id));
    }
  };

  // 播放全部歌曲
  const handlePlayAll = () => {
    if (artistSongs.length > 0) {
      dispatch(setCurrentSong(artistSongs[0]));
      dispatch(setPlaylist(artistSongs));
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  if (!artist) {
    return (
      <Card className="border-none shadow-lg">
        <div className="text-center py-12 text-gray-500">
          <UserOutlined className="text-4xl mb-4" />
          <div>歌手不存在</div>
          <Button
            type="primary"
            className="mt-4"
            onClick={() => navigate('/artists')}
          >
            返回歌手列表
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* 歌手信息头部 */}
      <Card className="border-none shadow-lg">
        <Row gutter={[24, 24]} align="middle">
          <Col xs={24} md={8} lg={6}>
            <div className="text-center">
              <div className="relative inline-block">
                <Avatar
                  src={artist.avatar}
                  size={200}
                  icon={<UserOutlined />}
                  className="shadow-lg"
                />
                {artist.verified && (
                  <div className="absolute -top-2 -right-2 bg-blue-500 text-white rounded-full p-2">
                    <StarOutlined className="text-lg" />
                  </div>
                )}
              </div>
            </div>
          </Col>

          <Col xs={24} md={16} lg={18}>
            <div className="space-y-4">
              <div>
                <Title level={1} className="mb-2 flex items-center">
                  {artist.name}
                  {artist.verified && (
                    <StarOutlined className="ml-2 text-blue-500" />
                  )}
                </Title>
                <div className="flex flex-wrap gap-2 mb-3">
                  <Tag color="blue">
                    {artist.region === 'chinese' ? '华语' :
                     artist.region === 'western' ? '欧美' :
                     artist.region === 'japanese' ? '日语' :
                     artist.region === 'korean' ? '韩语' : '其他'}
                  </Tag>
                  <Tag color="purple">
                    {artist.type === 'male' ? '男歌手' :
                     artist.type === 'female' ? '女歌手' : '组合'}
                  </Tag>
                </div>
                <Paragraph className="text-gray-600 dark:text-gray-300 text-lg">
                  {artist.description}
                </Paragraph>
              </div>

              {/* 统计信息 */}
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Statistic
                    title="粉丝数"
                    value={artist.followers}
                    formatter={(value) => formatPlayCount(Number(value))}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="歌曲数"
                    value={artistSongs.length}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="专辑数"
                    value={artistAlbums.length}
                  />
                </Col>
              </Row>

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3">
                <Button
                  type="primary"
                  size="large"
                  icon={<PlayCircleOutlined />}
                  onClick={handlePlayAll}
                  disabled={artistSongs.length === 0}
                >
                  播放全部
                </Button>
                <Button
                  size="large"
                  icon={<HeartOutlined />}
                  onClick={handleFollow}
                  className={isFollowed ? 'text-red-500 border-red-500' : ''}
                >
                  {isFollowed ? '已关注' : '关注'}
                </Button>
                <Button
                  size="large"
                  icon={<ShareAltOutlined />}
                >
                  分享
                </Button>
                <Button
                  size="large"
                  icon={<DownloadOutlined />}
                >
                  下载
                </Button>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 内容标签页 */}
      <Card className="border-none shadow-lg">
        <Tabs defaultActiveKey="songs" size="large">
          <TabPane tab={`热门歌曲 (${artistSongs.length})`} key="songs">
            {artistSongs.length > 0 ? (
              <SongList
                songs={artistSongs}
                showIndex={true}
                showAlbum={true}
                showPlayCount={true}
              />
            ) : (
              <div className="text-center py-12 text-gray-500">
                <PlayCircleOutlined className="text-4xl mb-4" />
                <div>暂无歌曲</div>
              </div>
            )}
          </TabPane>

          <TabPane tab={`专辑 (${artistAlbums.length})`} key="albums">
            {artistAlbums.length > 0 ? (
              <Row gutter={[16, 16]}>
                {artistAlbums.map((album) => (
                  <Col xs={12} sm={8} md={6} lg={4} key={album.id}>
                    <Card
                      hoverable
                      className="border-none shadow-md hover-card"
                      cover={
                        <img
                          src={album.cover}
                          alt={album.name}
                          className="w-full h-40 object-cover"
                        />
                      }
                      onClick={() => navigate(`/album/${album.id}`)}
                    >
                      <Card.Meta
                        title={
                          <div className="truncate text-sm font-medium">
                            {album.name}
                          </div>
                        }
                        description={
                          <div className="text-xs text-gray-500">
                            {new Date(album.releaseDate).getFullYear()}
                          </div>
                        }
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <PlayCircleOutlined className="text-4xl mb-4" />
                <div>暂无专辑</div>
              </div>
            )}
          </TabPane>

          <TabPane tab="相似歌手" key="similar">
            <div className="text-center py-12 text-gray-500">
              <UserOutlined className="text-4xl mb-4" />
              <div>相似歌手功能开发中...</div>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ArtistDetail;
