import React, { useEffect, useState } from 'react';
import { Card, Typography, Row, Col, Spin, Avatar, Button, Tag, Input, Select } from 'antd';
import {
  AppstoreOutlined,
  PlayCircleOutlined,
  UserOutlined,
  StarOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { mockApiService } from '../../services/mockApi';
import type { Playlist } from '../../types';
import { formatPlayCount } from '../../utils';

const { Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const Playlists: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [filteredPlaylists, setFilteredPlaylists] = useState<Playlist[]>([]);
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [sortBy, setSortBy] = useState<string>('playCount');

  // 歌单分类
  const categories = [
    { key: 'all', label: '全部歌单', icon: '🎵' },
    { key: '华语', label: '华语', icon: '🇨🇳' },
    { key: '欧美', label: '欧美', icon: '🇺🇸' },
    { key: '流行', label: '流行', icon: '🎤' },
    { key: '摇滚', label: '摇滚', icon: '🎸' },
    { key: '民谣', label: '民谣', icon: '🎻' },
    { key: '电子', label: '电子', icon: '🎛️' },
    { key: '古典', label: '古典', icon: '🎼' },
    { key: '爵士', label: '爵士', icon: '🎺' },
    { key: '说唱', label: '说唱', icon: '🎤' },
    { key: '轻音乐', label: '轻音乐', icon: '🎹' },
    { key: '影视', label: '影视原声', icon: '🎬' }
  ];

  // 排序选项
  const sortOptions = [
    { value: 'playCount', label: '播放量' },
    { value: 'createTime', label: '创建时间' },
    { value: 'updateTime', label: '更新时间' },
    { value: 'name', label: '名称' }
  ];

  // 加载歌单数据
  useEffect(() => {
    const loadPlaylists = async () => {
      setLoading(true);
      try {
        const response = await mockApiService.playlists.getFeaturedPlaylists();
        setPlaylists(response.data);
        setFilteredPlaylists(response.data);
      } catch (error) {
        console.error('Failed to load playlists:', error);
      } finally {
        setLoading(false);
      }
    };

    loadPlaylists();
  }, []);

  // 过滤和排序歌单
  useEffect(() => {
    let filtered = [...playlists];

    // 按分类过滤
    if (activeCategory !== 'all') {
      filtered = filtered.filter(playlist =>
        playlist.tags.includes(activeCategory)
      );
    }

    // 按搜索关键词过滤
    if (searchKeyword.trim()) {
      const keyword = searchKeyword.toLowerCase();
      filtered = filtered.filter(playlist =>
        playlist.name.toLowerCase().includes(keyword) ||
        playlist.description.toLowerCase().includes(keyword) ||
        playlist.creator.toLowerCase().includes(keyword)
      );
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'playCount':
          return b.playCount - a.playCount;
        case 'createTime':
          return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
        case 'updateTime':
          return new Date(b.updateTime).getTime() - new Date(a.updateTime).getTime();
        case 'name':
          return a.name.localeCompare(b.name);
        default:
          return 0;
      }
    });

    setFilteredPlaylists(filtered);
  }, [playlists, activeCategory, searchKeyword, sortBy]);

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchKeyword(value);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <Title level={2} className="mb-0 flex items-center">
          <AppstoreOutlined className="mr-3 text-green-500" />
          歌单广场
        </Title>
        <div className="flex items-center space-x-4">
          <Select
            value={sortBy}
            onChange={setSortBy}
            style={{ width: 120 }}
            placeholder="排序方式"
          >
            {sortOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
          <Search
            placeholder="搜索歌单..."
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            onChange={(e) => setSearchKeyword(e.target.value)}
          />
        </div>
      </div>

      {/* 分类标签 */}
      <Card className="border-none shadow-lg">
        <div className="flex flex-wrap gap-3">
          {categories.map(category => (
            <Tag
              key={category.key}
              color={activeCategory === category.key ? 'blue' : 'default'}
              className="cursor-pointer px-4 py-2 text-sm flex items-center space-x-1"
              onClick={() => setActiveCategory(category.key)}
            >
              <span>{category.icon}</span>
              <span>{category.label}</span>
            </Tag>
          ))}
        </div>
      </Card>

      {/* 歌单列表 */}
      <Row gutter={[16, 16]}>
        {filteredPlaylists.map((playlist) => (
          <Col xs={12} sm={8} md={6} lg={4} key={playlist.id}>
            <Card
              hoverable
              className="border-none shadow-md hover-card"
              cover={
                <div className="relative group">
                  <img
                    src={playlist.cover}
                    alt={playlist.name}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                    <PlayCircleOutlined className="text-white text-4xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform scale-75 group-hover:scale-100" />
                  </div>

                  {/* 播放次数 */}
                  <div className="absolute top-2 right-2 bg-black bg-opacity-60 text-white text-xs px-2 py-1 rounded backdrop-blur-sm flex items-center space-x-1">
                    <PlayCircleOutlined />
                    <span>{formatPlayCount(playlist.playCount)}</span>
                  </div>

                  {/* 歌曲数量 */}
                  <div className="absolute bottom-2 left-2 bg-white bg-opacity-90 text-gray-800 text-xs px-2 py-1 rounded">
                    {playlist.songs.length} 首
                  </div>

                  {/* 精品标识 */}
                  {playlist.playCount > 1000000 && (
                    <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded flex items-center space-x-1">
                      <StarOutlined />
                      <span>精品</span>
                    </div>
                  )}
                </div>
              }
              onClick={() => navigate(`/playlist/${playlist.id}`)}
            >
              <Card.Meta
                title={
                  <div className="space-y-2">
                    <div className="font-medium text-gray-900 dark:text-white truncate">
                      {playlist.name}
                    </div>
                    <div className="flex items-center space-x-2 text-xs text-gray-500">
                      <Avatar
                        size={16}
                        icon={<UserOutlined />}
                        className="flex-shrink-0"
                      />
                      <span className="truncate">{playlist.creator}</span>
                    </div>
                  </div>
                }
                description={
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500 line-clamp-2 h-8">
                      {playlist.description}
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {playlist.tags.slice(0, 2).map(tag => (
                        <Tag
                          key={tag}
                          className="text-xs px-1 py-0"
                          color="blue"
                        >
                          {tag}
                        </Tag>
                      ))}
                    </div>
                  </div>
                }
              />
            </Card>
          </Col>
        ))}
      </Row>

      {filteredPlaylists.length === 0 && (
        <Card className="border-none shadow-lg">
          <div className="text-center py-12 text-gray-500">
            <AppstoreOutlined className="text-4xl mb-4" />
            <div>没有找到符合条件的歌单</div>
            <div className="text-sm mt-2">请尝试调整筛选条件或搜索关键词</div>
          </div>
        </Card>
      )}

      {/* 加载更多按钮 */}
      {filteredPlaylists.length > 0 && (
        <div className="text-center">
          <Button size="large" className="px-8">
            加载更多
          </Button>
        </div>
      )}
    </div>
  );
};

export default Playlists;
