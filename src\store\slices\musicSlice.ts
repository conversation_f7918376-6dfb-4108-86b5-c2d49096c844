import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Song, Artist, Album, Playlist, Ranking, SearchResult } from '../../types';

interface MusicState {
  // 歌曲相关
  songs: Song[];
  currentSongs: Song[];
  
  // 歌手相关
  artists: Artist[];
  currentArtist: Artist | null;
  
  // 专辑相关
  albums: Album[];
  currentAlbum: Album | null;
  
  // 歌单相关
  playlists: Playlist[];
  currentPlaylist: Playlist | null;
  
  // 排行榜
  rankings: Ranking[];
  
  // 搜索相关
  searchResult: SearchResult | null;
  searchKeyword: string;
  searchHistory: string[];
  
  // 加载状态
  loading: {
    songs: boolean;
    artists: boolean;
    albums: boolean;
    playlists: boolean;
    rankings: boolean;
    search: boolean;
  };
  
  // 错误状态
  error: string | null;
}

const initialState: MusicState = {
  songs: [],
  currentSongs: [],
  artists: [],
  currentArtist: null,
  albums: [],
  currentAlbum: null,
  playlists: [],
  currentPlaylist: null,
  rankings: [],
  searchResult: null,
  searchKeyword: '',
  searchHistory: [],
  loading: {
    songs: false,
    artists: false,
    albums: false,
    playlists: false,
    rankings: false,
    search: false,
  },
  error: null,
};

const musicSlice = createSlice({
  name: 'music',
  initialState,
  reducers: {
    // 设置加载状态
    setLoading: (state, action: PayloadAction<{ key: keyof MusicState['loading']; value: boolean }>) => {
      state.loading[action.payload.key] = action.payload.value;
    },
    
    // 设置错误信息
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    
    // 歌曲相关
    setSongs: (state, action: PayloadAction<Song[]>) => {
      state.songs = action.payload;
    },
    
    setCurrentSongs: (state, action: PayloadAction<Song[]>) => {
      state.currentSongs = action.payload;
    },
    
    addSong: (state, action: PayloadAction<Song>) => {
      const exists = state.songs.find(song => song.id === action.payload.id);
      if (!exists) {
        state.songs.push(action.payload);
      }
    },
    
    // 歌手相关
    setArtists: (state, action: PayloadAction<Artist[]>) => {
      state.artists = action.payload;
    },
    
    setCurrentArtist: (state, action: PayloadAction<Artist | null>) => {
      state.currentArtist = action.payload;
    },
    
    addArtist: (state, action: PayloadAction<Artist>) => {
      const exists = state.artists.find(artist => artist.id === action.payload.id);
      if (!exists) {
        state.artists.push(action.payload);
      }
    },
    
    // 专辑相关
    setAlbums: (state, action: PayloadAction<Album[]>) => {
      state.albums = action.payload;
    },
    
    setCurrentAlbum: (state, action: PayloadAction<Album | null>) => {
      state.currentAlbum = action.payload;
    },
    
    addAlbum: (state, action: PayloadAction<Album>) => {
      const exists = state.albums.find(album => album.id === action.payload.id);
      if (!exists) {
        state.albums.push(action.payload);
      }
    },
    
    // 歌单相关
    setPlaylists: (state, action: PayloadAction<Playlist[]>) => {
      state.playlists = action.payload;
    },
    
    setCurrentPlaylist: (state, action: PayloadAction<Playlist | null>) => {
      state.currentPlaylist = action.payload;
    },
    
    addPlaylist: (state, action: PayloadAction<Playlist>) => {
      const exists = state.playlists.find(playlist => playlist.id === action.payload.id);
      if (!exists) {
        state.playlists.push(action.payload);
      }
    },
    
    updatePlaylist: (state, action: PayloadAction<Playlist>) => {
      const index = state.playlists.findIndex(playlist => playlist.id === action.payload.id);
      if (index !== -1) {
        state.playlists[index] = action.payload;
      }
      if (state.currentPlaylist?.id === action.payload.id) {
        state.currentPlaylist = action.payload;
      }
    },
    
    // 排行榜相关
    setRankings: (state, action: PayloadAction<Ranking[]>) => {
      state.rankings = action.payload;
    },
    
    // 搜索相关
    setSearchResult: (state, action: PayloadAction<SearchResult | null>) => {
      state.searchResult = action.payload;
    },
    
    setSearchKeyword: (state, action: PayloadAction<string>) => {
      state.searchKeyword = action.payload;
    },
    
    addSearchHistory: (state, action: PayloadAction<string>) => {
      const keyword = action.payload.trim();
      if (keyword && !state.searchHistory.includes(keyword)) {
        state.searchHistory.unshift(keyword);
        // 限制搜索历史数量
        if (state.searchHistory.length > 20) {
          state.searchHistory = state.searchHistory.slice(0, 20);
        }
      }
    },
    
    removeSearchHistory: (state, action: PayloadAction<string>) => {
      state.searchHistory = state.searchHistory.filter(keyword => keyword !== action.payload);
    },
    
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
    
    // 清空所有数据
    clearAll: (state) => {
      return initialState;
    },
  },
});

export const {
  setLoading,
  setError,
  setSongs,
  setCurrentSongs,
  addSong,
  setArtists,
  setCurrentArtist,
  addArtist,
  setAlbums,
  setCurrentAlbum,
  addAlbum,
  setPlaylists,
  setCurrentPlaylist,
  addPlaylist,
  updatePlaylist,
  setRankings,
  setSearchResult,
  setSearchKeyword,
  addSearchHistory,
  removeSearchHistory,
  clearSearchHistory,
  clearAll,
} = musicSlice.actions;

export default musicSlice.reducer;
