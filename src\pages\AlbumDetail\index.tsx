import React from 'react';
import { Card, Typography } from 'antd';
import { useParams } from 'react-router-dom';

const { Title } = Typography;

const AlbumDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  return (
    <div>
      <Title level={2}>专辑详情 - {id}</Title>
      <Card>
        <div className="text-center py-8 text-gray-500">
          专辑详情页面开发中...
        </div>
      </Card>
    </div>
  );
};

export default AlbumDetail;
