/* 基础样式 */
:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  min-height: 100vh;
  background-color: #ffffff;
  color: #1f2937;
}

.dark body {
  background-color: #0f172a;
  color: #f8fafc;
}

/* 基础布局样式 */
.music-player-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.h-full {
  height: 100%;
}

.flex-1 {
  flex: 1;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  background-color: #f9fafb;
}

.dark .content-area {
  background-color: #1e293b;
}

.p-6 {
  padding: 1.5rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.dark .bg-dark-800 {
  background-color: #1e293b;
}

/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* 音乐播放器专用样式 */
.music-player-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  overflow-y: auto;
}

.player-bar {
  height: 80px;
  border-top: 1px solid #e2e8f0;
}

.dark .player-bar {
  border-top-color: #334155;
}

/* 歌曲列表样式 */
.song-list-table .ant-table-tbody > tr:hover > td {
  background-color: #f8fafc !important;
}

.dark .song-list-table .ant-table-tbody > tr:hover > td {
  background-color: #1e293b !important;
}

.song-list-table .ant-table-tbody > tr > td {
  border-bottom: none !important;
  padding: 8px 16px !important;
}

/* 播放队列样式 */
.play-queue-drawer .ant-drawer-body {
  padding: 0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .music-player-container {
    flex-direction: column;
  }

  .player-bar {
    height: 70px;
    padding: 0 16px;
  }

  .content-area {
    padding: 16px !important;
  }
}

/* 加载动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* 音乐可视化效果 */
@keyframes musicWave {
  0%, 100% { height: 4px; }
  50% { height: 16px; }
}

.music-wave {
  display: inline-flex;
  align-items: center;
  gap: 2px;
}

.music-wave span {
  width: 2px;
  height: 4px;
  background-color: currentColor;
  animation: musicWave 1s ease-in-out infinite;
}

.music-wave span:nth-child(2) {
  animation-delay: 0.1s;
}

.music-wave span:nth-child(3) {
  animation-delay: 0.2s;
}

/* 卡片悬停效果 */
.hover-card {
  transition: all 0.3s ease;
}

.hover-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
