import { Song, Artist, Album, Playlist, Ranking, User } from '../types';

// 模拟歌曲数据
export const mockSongs: Song[] = [
  {
    id: '1',
    name: '稻香',
    artist: '周杰伦',
    artistId: 'artist1',
    album: '魔杰座',
    albumId: 'album1',
    duration: 223,
    url: '/audio/daoxiang.mp3',
    cover: 'https://picsum.photos/300/300?random=1',
    lyrics: '[00:00.00]稻香 - 周杰伦\n[00:15.00]对这个世界如果你有太多的抱怨\n[00:18.00]跌倒了就不敢继续往前走',
    playCount: 12345678,
    likeCount: 234567,
    releaseDate: '2008-10-15',
    genre: ['流行', '华语']
  },
  {
    id: '2',
    name: '<PERSON>hape of <PERSON>',
    artist: '<PERSON>',
    artistId: 'artist2',
    album: '÷ (Divide)',
    albumId: 'album2',
    duration: 233,
    url: '/audio/shape-of-you.mp3',
    cover: 'https://picsum.photos/300/300?random=2',
    playCount: 23456789,
    likeCount: 345678,
    releaseDate: '2017-01-06',
    genre: ['流行', '欧美']
  },
  {
    id: '3',
    name: 'Dynamite',
    artist: 'BTS',
    artistId: 'artist3',
    album: 'BE',
    albumId: 'album3',
    duration: 199,
    url: '/audio/dynamite.mp3',
    cover: 'https://picsum.photos/300/300?random=3',
    playCount: 34567890,
    likeCount: 456789,
    releaseDate: '2020-08-21',
    genre: ['流行', '韩语']
  }
];

// 模拟歌手数据
export const mockArtists: Artist[] = [
  {
    id: 'artist1',
    name: '周杰伦',
    avatar: 'https://picsum.photos/200/200?random=11',
    description: '华语流行音乐天王',
    region: 'chinese',
    type: 'male',
    songs: [mockSongs[0]],
    albums: [],
    followers: 12345678,
    verified: true
  },
  {
    id: 'artist2',
    name: 'Ed Sheeran',
    avatar: 'https://picsum.photos/200/200?random=12',
    description: '英国创作歌手',
    region: 'western',
    type: 'male',
    songs: [mockSongs[1]],
    albums: [],
    followers: 23456789,
    verified: true
  },
  {
    id: 'artist3',
    name: 'BTS',
    avatar: 'https://picsum.photos/200/200?random=13',
    description: '韩国男子组合',
    region: 'korean',
    type: 'group',
    songs: [mockSongs[2]],
    albums: [],
    followers: 34567890,
    verified: true
  }
];

// 模拟专辑数据
export const mockAlbums: Album[] = [
  {
    id: 'album1',
    name: '魔杰座',
    cover: 'https://picsum.photos/300/300?random=21',
    artist: '周杰伦',
    artistId: 'artist1',
    releaseDate: '2008-10-15',
    description: '周杰伦第九张录音室专辑',
    songs: [mockSongs[0]],
    genre: ['流行', '华语'],
    playCount: 12345678
  },
  {
    id: 'album2',
    name: '÷ (Divide)',
    cover: 'https://picsum.photos/300/300?random=22',
    artist: 'Ed Sheeran',
    artistId: 'artist2',
    releaseDate: '2017-03-03',
    description: 'Ed Sheeran第三张录音室专辑',
    songs: [mockSongs[1]],
    genre: ['流行', '欧美'],
    playCount: 23456789
  }
];

// 模拟歌单数据
export const mockPlaylists: Playlist[] = [
  {
    id: 'playlist1',
    name: '华语流行精选',
    description: '最受欢迎的华语流行歌曲合集',
    cover: 'https://picsum.photos/300/300?random=31',
    creator: '音乐小编',
    creatorId: 'user1',
    songs: [mockSongs[0]],
    playCount: 1234567,
    createTime: '2023-01-01',
    updateTime: '2024-01-01',
    tags: ['华语', '流行', '精选'],
    isPublic: true
  },
  {
    id: 'playlist2',
    name: '欧美热门单曲',
    description: 'Billboard热门歌曲推荐',
    cover: 'https://picsum.photos/300/300?random=32',
    creator: '音乐达人',
    creatorId: 'user2',
    songs: [mockSongs[1]],
    playCount: 987654,
    createTime: '2023-02-01',
    updateTime: '2024-02-01',
    tags: ['欧美', '流行', '热门'],
    isPublic: true
  }
];

// 模拟排行榜数据
export const mockRankings: Ranking[] = [
  {
    id: 'ranking1',
    name: '热歌榜',
    description: '最热门的歌曲排行',
    cover: 'https://picsum.photos/300/300?random=41',
    songs: mockSongs.map((song, index) => ({
      ...song,
      rank: index + 1,
      lastRank: index + 2,
      trend: 'up' as const
    })),
    updateTime: '2024-01-01'
  },
  {
    id: 'ranking2',
    name: '新歌榜',
    description: '最新发布的歌曲排行',
    cover: 'https://picsum.photos/300/300?random=42',
    songs: mockSongs.map((song, index) => ({
      ...song,
      rank: index + 1,
      lastRank: 0,
      trend: 'new' as const
    })),
    updateTime: '2024-01-01'
  }
];

// 模拟用户数据
export const mockUser: User = {
  id: 'user1',
  username: 'musiclover',
  email: '<EMAIL>',
  avatar: 'https://picsum.photos/100/100?random=51',
  nickname: '音乐爱好者',
  bio: '热爱音乐的普通用户',
  likedSongs: ['1', '2'],
  likedPlaylists: ['playlist1'],
  likedAlbums: ['album1'],
  followedArtists: ['artist1', 'artist2'],
  createdPlaylists: ['playlist1'],
  playHistory: [
    {
      songId: '1',
      playTime: '2024-01-01T10:00:00Z',
      duration: 223
    },
    {
      songId: '2',
      playTime: '2024-01-01T09:30:00Z',
      duration: 233
    }
  ],
  preferences: {
    theme: 'light',
    volume: 0.8,
    playMode: 'sequence',
    autoPlay: true,
    showLyrics: true
  }
};
