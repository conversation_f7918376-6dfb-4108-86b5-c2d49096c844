import { Howl, Howler } from 'howler';
import { Song } from '../types';

export class AudioService {
  private currentHowl: Howl | null = null;
  private currentSong: Song | null = null;
  private onTimeUpdate?: (currentTime: number) => void;
  private onEnd?: () => void;
  private onLoad?: (duration: number) => void;
  private onError?: (error: any) => void;
  private timeUpdateInterval?: NodeJS.Timeout;

  constructor() {
    // 设置全局音频配置
    Howler.volume(0.8);
  }

  // 加载并播放歌曲
  async loadAndPlay(song: Song): Promise<void> {
    try {
      // 停止当前播放
      this.stop();

      this.currentSong = song;

      // 创建新的Howl实例
      this.currentHowl = new Howl({
        src: [song.url],
        html5: true, // 使用HTML5音频以支持流式播放
        preload: true,
        onload: () => {
          if (this.currentHowl) {
            const duration = this.currentHowl.duration();
            this.onLoad?.(duration);
          }
        },
        onplay: () => {
          this.startTimeUpdate();
        },
        onpause: () => {
          this.stopTimeUpdate();
        },
        onstop: () => {
          this.stopTimeUpdate();
        },
        onend: () => {
          this.stopTimeUpdate();
          this.onEnd?.();
        },
        onerror: (id, error) => {
          console.error('Audio error:', error);
          this.onError?.(error);
        }
      });

      // 开始播放
      this.currentHowl.play();
    } catch (error) {
      console.error('Failed to load audio:', error);
      this.onError?.(error);
    }
  }

  // 播放
  play(): void {
    if (this.currentHowl && !this.currentHowl.playing()) {
      this.currentHowl.play();
    }
  }

  // 暂停
  pause(): void {
    if (this.currentHowl && this.currentHowl.playing()) {
      this.currentHowl.pause();
    }
  }

  // 停止
  stop(): void {
    if (this.currentHowl) {
      this.currentHowl.stop();
      this.currentHowl.unload();
      this.currentHowl = null;
    }
    this.stopTimeUpdate();
    this.currentSong = null;
  }

  // 设置音量 (0-1)
  setVolume(volume: number): void {
    const clampedVolume = Math.max(0, Math.min(1, volume));
    Howler.volume(clampedVolume);
  }

  // 获取音量
  getVolume(): number {
    return Howler.volume();
  }

  // 设置播放位置 (秒)
  seek(time: number): void {
    if (this.currentHowl) {
      this.currentHowl.seek(time);
    }
  }

  // 获取当前播放位置 (秒)
  getCurrentTime(): number {
    if (this.currentHowl) {
      const seek = this.currentHowl.seek();
      return typeof seek === 'number' ? seek : 0;
    }
    return 0;
  }

  // 获取歌曲总时长 (秒)
  getDuration(): number {
    if (this.currentHowl) {
      return this.currentHowl.duration() || 0;
    }
    return 0;
  }

  // 检查是否正在播放
  isPlaying(): boolean {
    return this.currentHowl ? this.currentHowl.playing() : false;
  }

  // 检查是否已加载
  isLoaded(): boolean {
    return this.currentHowl ? this.currentHowl.state() === 'loaded' : false;
  }

  // 获取当前歌曲
  getCurrentSong(): Song | null {
    return this.currentSong;
  }

  // 设置时间更新回调
  setOnTimeUpdate(callback: (currentTime: number) => void): void {
    this.onTimeUpdate = callback;
  }

  // 设置播放结束回调
  setOnEnd(callback: () => void): void {
    this.onEnd = callback;
  }

  // 设置加载完成回调
  setOnLoad(callback: (duration: number) => void): void {
    this.onLoad = callback;
  }

  // 设置错误回调
  setOnError(callback: (error: any) => void): void {
    this.onError = callback;
  }

  // 开始时间更新
  private startTimeUpdate(): void {
    this.stopTimeUpdate();
    this.timeUpdateInterval = setInterval(() => {
      if (this.currentHowl && this.currentHowl.playing()) {
        const currentTime = this.getCurrentTime();
        this.onTimeUpdate?.(currentTime);
      }
    }, 1000); // 每秒更新一次
  }

  // 停止时间更新
  private stopTimeUpdate(): void {
    if (this.timeUpdateInterval) {
      clearInterval(this.timeUpdateInterval);
      this.timeUpdateInterval = undefined;
    }
  }

  // 销毁服务
  destroy(): void {
    this.stop();
    this.onTimeUpdate = undefined;
    this.onEnd = undefined;
    this.onLoad = undefined;
    this.onError = undefined;
  }
}

// 创建全局音频服务实例
export const audioService = new AudioService();
