import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Avatar, Typography } from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  HeartOutlined,
  DownOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { 
  togglePlay, 
  nextSong, 
  prevSong, 
  setVolume, 
  setCurrentTime 
} from '../../store/slices/playerSlice';
import { formatTime } from '../../utils';
import { useAudioPlayer } from '../../hooks/useAudioPlayer';

const { Text, Title } = Typography;

interface MobilePlayerProps {
  visible: boolean;
  onClose: () => void;
}

const MobilePlayer: React.FC<MobilePlayerProps> = ({ visible, onClose }) => {
  const dispatch = useDispatch();
  const { 
    currentSong, 
    isPlaying, 
    currentTime, 
    duration, 
    volume 
  } = useSelector((state: RootState) => state.player);
  
  const { togglePlayPause, seekTo } = useAudioPlayer();

  const handlePlayPause = () => {
    togglePlayPause();
  };

  const handlePrevious = () => {
    dispatch(prevSong());
  };

  const handleNext = () => {
    dispatch(nextSong());
  };

  const handleProgressChange = (value: number) => {
    seekTo(value);
  };

  if (!currentSong) {
    return null;
  }

  return (
    <Drawer
      placement="bottom"
      height="100vh"
      open={visible}
      onClose={onClose}
      className="mobile-player-drawer"
      headerStyle={{ display: 'none' }}
      bodyStyle={{ padding: 0 }}
    >
      <div className="h-full bg-gradient-to-b from-gray-900 to-black text-white flex flex-col">
        {/* 顶部控制栏 */}
        <div className="flex items-center justify-between p-4">
          <Button
            type="text"
            icon={<DownOutlined />}
            onClick={onClose}
            className="text-white"
          />
          <Text className="text-white font-medium">正在播放</Text>
          <div className="w-8" />
        </div>

        {/* 专辑封面 */}
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="relative">
            <Avatar 
              src={currentSong.cover} 
              size={280}
              className="rounded-2xl shadow-2xl"
            />
            {/* 旋转动画效果 */}
            <div className={`absolute inset-0 rounded-2xl ${isPlaying ? 'animate-spin-slow' : ''}`}>
              <div className="w-full h-full border-4 border-white/20 rounded-2xl"></div>
            </div>
          </div>
        </div>

        {/* 歌曲信息 */}
        <div className="px-8 pb-4 text-center">
          <Title level={3} className="text-white mb-2 truncate">
            {currentSong.name}
          </Title>
          <Text className="text-gray-300 text-lg">
            {currentSong.artist}
          </Text>
        </div>

        {/* 进度条 */}
        <div className="px-8 pb-6">
          <Slider
            value={currentTime}
            max={duration}
            onChange={handleProgressChange}
            className="mb-2"
            tooltip={{ formatter: (value) => formatTime(value || 0) }}
          />
          <div className="flex justify-between text-sm text-gray-400">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-8 pb-8">
          <Button
            type="text"
            icon={<HeartOutlined />}
            size="large"
            className="text-white text-2xl"
          />
          
          <Button
            type="text"
            icon={<StepBackwardOutlined />}
            size="large"
            onClick={handlePrevious}
            className="text-white text-3xl"
          />
          
          <Button
            type="text"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            size="large"
            onClick={handlePlayPause}
            className="text-white text-5xl bg-white/10 rounded-full p-4"
          />
          
          <Button
            type="text"
            icon={<StepForwardOutlined />}
            size="large"
            onClick={handleNext}
            className="text-white text-3xl"
          />
          
          <Button
            type="text"
            icon={<div className="text-lg">⋯</div>}
            size="large"
            className="text-white text-2xl"
          />
        </div>

        {/* 底部安全区域 */}
        <div className="h-8 bg-black/50"></div>
      </div>
    </Drawer>
  );
};

export default MobilePlayer;
