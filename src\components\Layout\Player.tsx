import React, { useState } from 'react';
import { <PERSON><PERSON>, Slider, Avatar, message } from 'antd';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  SoundOutlined,
  HeartOutlined,
  UnorderedListOutlined,
  RetweetOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import {
  togglePlay,
  nextSong,
  prevSong,
  setVolume,
  setCurrentTime,
  setPlayMode
} from '../../store/slices/playerSlice';
import { addLikedSong, removeLikedSong } from '../../store/slices/userSlice';
import { formatTime, isMobile } from '../../utils';
import { useAudioPlayer } from '../../hooks/useAudioPlayer';
import PlayQueue from '../PlayQueue';
import MobilePlayer from '../MobilePlayer';

const Player: React.FC = () => {
  const dispatch = useDispatch();
  const [showPlayQueue, setShowPlayQueue] = useState(false);
  const [showMobilePlayer, setShowMobilePlayer] = useState(false);
  const [isMobileDevice, setIsMobileDevice] = useState(false);
  const {
    currentSong,
    isPlaying,
    currentTime,
    duration,
    volume,
    playMode
  } = useSelector((state: RootState) => state.player);

  const { currentUser, isLoggedIn } = useSelector((state: RootState) => state.user);

  // 使用音频播放器Hook
  const { togglePlayPause, seekTo, setVolumeLevel } = useAudioPlayer();

  // 检测移动设备
  React.useEffect(() => {
    setIsMobileDevice(isMobile());
  }, []);

  const handlePlayPause = () => {
    togglePlayPause();
  };

  const handlePrevious = () => {
    dispatch(prevSong());
  };

  const handleNext = () => {
    dispatch(nextSong());
  };

  const handleVolumeChange = (value: number) => {
    const newVolume = value / 100;
    dispatch(setVolume(newVolume));
    setVolumeLevel(newVolume);
  };

  const handleProgressChange = (value: number) => {
    seekTo(value);
  };

  const handlePlayModeChange = () => {
    const modes: Array<'sequence' | 'random' | 'repeat'> = ['sequence', 'random', 'repeat'];
    const currentIndex = modes.indexOf(playMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    dispatch(setPlayMode(nextMode));

    // 显示模式切换提示
    const modeNames = {
      sequence: '顺序播放',
      random: '随机播放',
      repeat: '单曲循环'
    };
    message.info(`已切换到${modeNames[nextMode]}`);
  };

  // 处理喜欢/取消喜欢
  const handleLike = () => {
    if (!isLoggedIn) {
      message.warning('请先登录');
      return;
    }

    if (!currentSong) return;

    const isLiked = currentUser?.likedSongs.includes(currentSong.id);

    if (isLiked) {
      dispatch(removeLikedSong(currentSong.id));
      message.success('已取消收藏');
    } else {
      dispatch(addLikedSong(currentSong.id));
      message.success('已添加到我喜欢的音乐');
    }
  };

  const getPlayModeIcon = () => {
    switch (playMode) {
      case 'random':
        return '🔀';
      case 'repeat':
        return '🔂';
      default:
        return '🔁';
    }
  };

  const getPlayModeText = () => {
    switch (playMode) {
      case 'random':
        return '随机播放';
      case 'repeat':
        return '单曲循环';
      default:
        return '顺序播放';
    }
  };

  if (!currentSong) {
    return (
      <div className="player-bar bg-white dark:bg-dark-900 flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">
          暂无播放内容
        </div>
      </div>
    );
  }

  // 移动端点击播放器区域打开全屏播放器
  const handlePlayerClick = () => {
    if (isMobileDevice && currentSong) {
      setShowMobilePlayer(true);
    }
  };

  return (
    <div
      className={`player-bar bg-white dark:bg-dark-900 px-6 flex items-center justify-between ${
        isMobileDevice ? 'cursor-pointer' : ''
      }`}
      onClick={isMobileDevice ? handlePlayerClick : undefined}
    >
      {/* 左侧：歌曲信息 */}
      <div className="flex items-center space-x-4 w-1/4">
        <Avatar 
          src={currentSong.cover} 
          size={48}
          className="rounded-lg"
        />
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
            {currentSong.name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {currentSong.artist}
          </div>
        </div>
        <Button
          type="text"
          icon={<HeartOutlined />}
          size="small"
          onClick={handleLike}
          className={`${
            currentUser?.likedSongs.includes(currentSong.id)
              ? 'text-red-500 hover:text-red-600'
              : 'text-gray-500 hover:text-red-500'
          }`}
        />
      </div>

      {/* 中间：播放控制 */}
      <div className="flex-1 max-w-2xl mx-8">
        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-4 mb-2">
          <Button
            type="text"
            icon={<span className="text-lg">{getPlayModeIcon()}</span>}
            size="small"
            title={getPlayModeText()}
            onClick={handlePlayModeChange}
            className="text-gray-500 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={<StepBackwardOutlined />}
            size="large"
            onClick={handlePrevious}
            className="text-gray-700 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            size="large"
            onClick={handlePlayPause}
            className="text-primary-600 hover:text-primary-700 text-3xl"
          />
          
          <Button
            type="text"
            icon={<StepForwardOutlined />}
            size="large"
            onClick={handleNext}
            className="text-gray-700 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={<UnorderedListOutlined />}
            size="small"
            onClick={() => setShowPlayQueue(true)}
            className="text-gray-500 hover:text-primary-600"
          />
        </div>

        {/* 进度条 */}
        <div className="flex items-center space-x-3">
          <span className="text-xs text-gray-500 w-10 text-right">
            {formatTime(currentTime)}
          </span>
          <Slider
            value={currentTime}
            max={duration}
            onChange={handleProgressChange}
            className="flex-1"
            tooltip={{ formatter: (value) => formatTime(value || 0) }}
          />
          <span className="text-xs text-gray-500 w-10">
            {formatTime(duration)}
          </span>
        </div>
      </div>

      {/* 右侧：音量控制 */}
      <div className="flex items-center space-x-3 w-1/4 justify-end">
        <SoundOutlined className="text-gray-500" />
        <Slider
          value={volume * 100}
          onChange={handleVolumeChange}
          className="w-24"
          tooltip={{ formatter: (value) => `${value}%` }}
        />
      </div>

      {/* 播放队列 */}
      <PlayQueue
        visible={showPlayQueue}
        onClose={() => setShowPlayQueue(false)}
      />

      {/* 移动端全屏播放器 */}
      <MobilePlayer
        visible={showMobilePlayer}
        onClose={() => setShowMobilePlayer(false)}
      />
    </div>
  );
};

export default Player;
