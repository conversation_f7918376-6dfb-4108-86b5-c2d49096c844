import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Avatar } from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  SoundOutlined,
  HeartOutlined,
  UnorderedListOutlined,
  RetweetOutlined
} from '@ant-design/icons';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { 
  togglePlay, 
  nextSong, 
  prevSong, 
  setVolume, 
  setCurrentTime,
  setPlayMode 
} from '../../store/slices/playerSlice';
import { formatTime } from '../../utils';

const Player: React.FC = () => {
  const dispatch = useDispatch();
  const { 
    currentSong, 
    isPlaying, 
    currentTime, 
    duration, 
    volume, 
    playMode 
  } = useSelector((state: RootState) => state.player);

  const handlePlayPause = () => {
    dispatch(togglePlay());
  };

  const handlePrevious = () => {
    dispatch(prevSong());
  };

  const handleNext = () => {
    dispatch(nextSong());
  };

  const handleVolumeChange = (value: number) => {
    dispatch(setVolume(value / 100));
  };

  const handleProgressChange = (value: number) => {
    dispatch(setCurrentTime(value));
  };

  const handlePlayModeChange = () => {
    const modes: Array<'sequence' | 'random' | 'repeat'> = ['sequence', 'random', 'repeat'];
    const currentIndex = modes.indexOf(playMode);
    const nextMode = modes[(currentIndex + 1) % modes.length];
    dispatch(setPlayMode(nextMode));
  };

  const getPlayModeIcon = () => {
    switch (playMode) {
      case 'random':
        return '🔀';
      case 'repeat':
        return '🔂';
      default:
        return '🔁';
    }
  };

  const getPlayModeText = () => {
    switch (playMode) {
      case 'random':
        return '随机播放';
      case 'repeat':
        return '单曲循环';
      default:
        return '顺序播放';
    }
  };

  if (!currentSong) {
    return (
      <div className="player-bar bg-white dark:bg-dark-900 flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">
          暂无播放内容
        </div>
      </div>
    );
  }

  return (
    <div className="player-bar bg-white dark:bg-dark-900 px-6 flex items-center justify-between">
      {/* 左侧：歌曲信息 */}
      <div className="flex items-center space-x-4 w-1/4">
        <Avatar 
          src={currentSong.cover} 
          size={48}
          className="rounded-lg"
        />
        <div className="flex-1 min-w-0">
          <div className="text-sm font-medium text-gray-900 dark:text-white truncate">
            {currentSong.name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 truncate">
            {currentSong.artist}
          </div>
        </div>
        <Button 
          type="text" 
          icon={<HeartOutlined />} 
          size="small"
          className="text-gray-500 hover:text-red-500"
        />
      </div>

      {/* 中间：播放控制 */}
      <div className="flex-1 max-w-2xl mx-8">
        {/* 控制按钮 */}
        <div className="flex items-center justify-center space-x-4 mb-2">
          <Button
            type="text"
            icon={<span className="text-lg">{getPlayModeIcon()}</span>}
            size="small"
            title={getPlayModeText()}
            onClick={handlePlayModeChange}
            className="text-gray-500 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={<StepBackwardOutlined />}
            size="large"
            onClick={handlePrevious}
            className="text-gray-700 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            size="large"
            onClick={handlePlayPause}
            className="text-primary-600 hover:text-primary-700 text-3xl"
          />
          
          <Button
            type="text"
            icon={<StepForwardOutlined />}
            size="large"
            onClick={handleNext}
            className="text-gray-700 hover:text-primary-600"
          />
          
          <Button
            type="text"
            icon={<UnorderedListOutlined />}
            size="small"
            className="text-gray-500 hover:text-primary-600"
          />
        </div>

        {/* 进度条 */}
        <div className="flex items-center space-x-3">
          <span className="text-xs text-gray-500 w-10 text-right">
            {formatTime(currentTime)}
          </span>
          <Slider
            value={currentTime}
            max={duration}
            onChange={handleProgressChange}
            className="flex-1"
            tooltip={{ formatter: (value) => formatTime(value || 0) }}
          />
          <span className="text-xs text-gray-500 w-10">
            {formatTime(duration)}
          </span>
        </div>
      </div>

      {/* 右侧：音量控制 */}
      <div className="flex items-center space-x-3 w-1/4 justify-end">
        <SoundOutlined className="text-gray-500" />
        <Slider
          value={volume * 100}
          onChange={handleVolumeChange}
          className="w-24"
          tooltip={{ formatter: (value) => `${value}%` }}
        />
      </div>
    </div>
  );
};

export default Player;
