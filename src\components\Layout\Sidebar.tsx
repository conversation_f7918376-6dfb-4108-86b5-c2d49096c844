import React from 'react';
import { Layout, Menu } from 'antd';
import { 
  HomeOutlined,
  TrophyOutlined,
  UserOutlined,
  AppstoreOutlined,
  HeartOutlined,
  PlaySquareOutlined,
  HistoryOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

const { Sider } = Layout;

const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { isLoggedIn } = useSelector((state: RootState) => state.user);

  // 主菜单项
  const mainMenuItems = [
    {
      key: '/',
      icon: <HomeOutlined />,
      label: '首页',
    },
    {
      key: '/ranking',
      icon: <TrophyOutlined />,
      label: '排行榜',
    },
    {
      key: '/artists',
      icon: <UserOutlined />,
      label: '歌手',
    },
    {
      key: '/playlists',
      icon: <AppstoreOutlined />,
      label: '歌单广场',
    },
  ];

  // 我的音乐菜单项（需要登录）
  const myMusicItems = isLoggedIn ? [
    {
      key: 'my-music',
      label: '我的音乐',
      type: 'group' as const,
      children: [
        {
          key: '/profile/liked',
          icon: <HeartOutlined />,
          label: '我喜欢的音乐',
        },
        {
          key: '/profile/playlists',
          icon: <PlaySquareOutlined />,
          label: '创建的歌单',
        },
        {
          key: '/profile/history',
          icon: <HistoryOutlined />,
          label: '最近播放',
        },
      ],
    },
  ] : [];

  // 创建歌单按钮
  const createPlaylistItem = isLoggedIn ? [
    {
      key: 'create-playlist',
      icon: <PlusOutlined />,
      label: '创建歌单',
      className: 'text-primary-600 hover:text-primary-700',
    },
  ] : [];

  const allMenuItems = [
    ...mainMenuItems,
    ...myMusicItems,
    ...createPlaylistItem,
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'create-playlist') {
      // 处理创建歌单逻辑
      console.log('创建歌单');
      return;
    }
    navigate(key);
  };

  return (
    <Sider 
      width={240} 
      className="bg-white dark:bg-dark-900 border-r border-gray-200 dark:border-dark-700"
      theme="light"
    >
      <div className="h-full flex flex-col">
        {/* 主菜单 */}
        <div className="flex-1 py-4">
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={allMenuItems}
            onClick={handleMenuClick}
            className="border-none bg-transparent"
            style={{
              backgroundColor: 'transparent',
            }}
          />
        </div>

        {/* 底部信息 */}
        {!isLoggedIn && (
          <div className="p-4 border-t border-gray-200 dark:border-dark-700">
            <div className="text-center">
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-2">
                登录后享受更多功能
              </p>
              <button
                onClick={() => navigate('/login')}
                className="w-full px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm"
              >
                立即登录
              </button>
            </div>
          </div>
        )}
      </div>
    </Sider>
  );
};

export default Sidebar;
